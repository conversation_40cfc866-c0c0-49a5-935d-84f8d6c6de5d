2025-07-10 15:24:09,961 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 15:24:09,967 - INFO - Initialized scraping run: 8f29649f-0ffd-412b-8456-d735ffd9268b
2025-07-10 15:24:09,968 - INFO - 🚀 Starting alternative CIDB contractor scraping...
2025-07-10 15:24:10,815 - INFO - 🔍 Exploring alternative CIDB contractor search endpoint...
2025-07-10 15:24:41,081 - ERROR - Failed to load alternative endpoint: Unexpected error in _crawl_web at line 744 in _crawl_web (../../Library/Python/3.9/lib/python/site-packages/crawl4ai/async_crawler_strategy.py):
Error: Failed on navigating ACS-GOTO:
Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "http://registers.cidb.org.za/PublicContractors/ContractorSearch", waiting until "domcontentloaded"


Code context:
 739                       response = await page.goto(
 740                           url, wait_until=config.wait_until, timeout=config.page_timeout
 741                       )
 742                       redirected_url = page.url
 743                   except Error as e:
 744 →                     raise RuntimeError(f"Failed on navigating ACS-GOTO:\n{str(e)}")
 745   
 746                   await self.execute_hook(
 747                       "after_goto", page, context=context, url=url, response=response, config=config
 748                   )
 749   
2025-07-10 15:24:41,081 - INFO - 🔍 Attempting to use search functionality...
2025-07-10 15:25:56,546 - WARNING - ❌ No contractor data could be extracted from alternative endpoint
2025-07-10 15:25:57,290 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.8f29649f-0ffd-412b-8456-d735ffd9268b "HTTP/2 200 OK"
2025-07-10 15:25:57,298 - INFO - Updated scraping run 8f29649f-0ffd-412b-8456-d735ffd9268b with status: failed
2025-07-10 15:25:57,299 - INFO - 
🎯 Alternative scraping completed in 107.60 seconds
Run ID: 8f29649f-0ffd-412b-8456-d735ffd9268b
Status: failed
Total Contractors: 0
Errors: 1
            
