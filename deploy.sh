#!/bin/bash

# CIDB Scraper Deployment Script
# This script sets up the environment and deploys the CIDB scraper

set -e

echo "🚀 Starting CIDB Scraper Deployment..."

# Check if Python 3.8+ is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Python $PYTHON_VERSION is installed, but Python $REQUIRED_VERSION or higher is required."
    exit 1
fi

echo "✅ Python $PYTHON_VERSION detected"

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Install Playwright browsers (required for Crawl4AI)
echo "🌐 Installing Playwright browsers..."
playwright install chromium

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️ .env file not found. Please create one based on .env.example"
    echo "📋 Example .env file:"
    cat .env.example
    echo ""
    echo "Please create .env file with your Supabase credentials and run this script again."
    exit 1
fi

# Load environment variables
source .env

# Validate environment variables
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
    echo "❌ Missing required environment variables. Please check your .env file."
    echo "Required variables: SUPABASE_URL, SUPABASE_KEY"
    exit 1
fi

echo "✅ Environment variables validated"

# Test the scraper
echo "🧪 Testing the scraper..."
python3 -c "
import asyncio
from cidb_scraper import CIDBScraper

async def test():
    scraper = CIDBScraper()
    print('✅ Scraper initialized successfully')
    print(f'📊 Run ID: {scraper.stats.run_id}')

asyncio.run(test())
"

if [ $? -eq 0 ]; then
    echo "✅ Scraper test passed"
else
    echo "❌ Scraper test failed"
    exit 1
fi

# Create systemd service file (optional)
read -p "🔧 Do you want to create a systemd service for scheduled scraping? (y/n): " create_service

if [ "$create_service" = "y" ] || [ "$create_service" = "Y" ]; then
    CURRENT_DIR=$(pwd)
    SERVICE_FILE="/etc/systemd/system/cidb-scraper.service"
    
    echo "📝 Creating systemd service..."
    
    sudo tee $SERVICE_FILE > /dev/null <<EOF
[Unit]
Description=CIDB Contractor Scraper
After=network.target

[Service]
Type=oneshot
User=$USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin
ExecStart=$CURRENT_DIR/venv/bin/python $CURRENT_DIR/cidb_scraper.py
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # Create timer for daily execution
    TIMER_FILE="/etc/systemd/system/cidb-scraper.timer"
    
    sudo tee $TIMER_FILE > /dev/null <<EOF
[Unit]
Description=Run CIDB Scraper Daily
Requires=cidb-scraper.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

    # Reload systemd and enable timer
    sudo systemctl daemon-reload
    sudo systemctl enable cidb-scraper.timer
    sudo systemctl start cidb-scraper.timer
    
    echo "✅ Systemd service and timer created"
    echo "📅 Scraper will run daily. Check status with: sudo systemctl status cidb-scraper.timer"
fi

# Create cron job (alternative to systemd)
if [ "$create_service" != "y" ] && [ "$create_service" != "Y" ]; then
    read -p "📅 Do you want to create a daily cron job instead? (y/n): " create_cron
    
    if [ "$create_cron" = "y" ] || [ "$create_cron" = "Y" ]; then
        CURRENT_DIR=$(pwd)
        CRON_JOB="0 2 * * * cd $CURRENT_DIR && $CURRENT_DIR/venv/bin/python $CURRENT_DIR/cidb_scraper.py >> $CURRENT_DIR/cron.log 2>&1"
        
        # Add cron job
        (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
        
        echo "✅ Cron job created - scraper will run daily at 2 AM"
        echo "📋 View cron jobs with: crontab -l"
        echo "📄 Check logs in: $CURRENT_DIR/cron.log"
    fi
fi

echo ""
echo "🎉 CIDB Scraper deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Run a test scrape: python3 cidb_scraper.py"
echo "2. Check logs: tail -f cidb_scraper.log"
echo "3. Monitor database: Check your Supabase dashboard"
echo ""
echo "🔧 Useful commands:"
echo "- Activate virtual environment: source venv/bin/activate"
echo "- Run scraper manually: python3 cidb_scraper.py"
echo "- View logs: tail -f cidb_scraper.log"
echo "- Check systemd service: sudo systemctl status cidb-scraper.service"
echo "- Check systemd timer: sudo systemctl status cidb-scraper.timer"
echo ""
echo "✨ Happy scraping!"
