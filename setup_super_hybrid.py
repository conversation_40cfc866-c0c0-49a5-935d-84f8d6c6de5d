#!/usr/bin/env python3
"""
Super Hybrid Scraper Setup Script
Installs dependencies and configures the environment
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a shell command with error handling"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_system_dependencies():
    """Install system-level dependencies"""
    print("\n📦 Installing system dependencies...")
    
    # Detect OS and install accordingly
    if sys.platform.startswith('linux'):
        # Ubuntu/Debian
        commands = [
            "sudo apt-get update",
            "sudo apt-get install -y tesseract-ocr tesseract-ocr-eng",
            "sudo apt-get install -y chromium-browser chromium-chromedriver"
        ]
        for cmd in commands:
            if not run_command(cmd, f"Running: {cmd}"):
                print("⚠️ Some system dependencies may not be installed")
                
    elif sys.platform == 'darwin':
        # macOS
        if shutil.which('brew'):
            commands = [
                "brew install tesseract",
                "brew install chromium"
            ]
            for cmd in commands:
                run_command(cmd, f"Running: {cmd}")
        else:
            print("⚠️ Homebrew not found. Please install tesseract manually:")
            print("   brew install tesseract")
            
    elif sys.platform.startswith('win'):
        # Windows
        print("⚠️ Windows detected. Please install manually:")
        print("   1. Tesseract: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   2. Chrome browser")
        
    else:
        print(f"⚠️ Unsupported OS: {sys.platform}")

def install_python_dependencies():
    """Install Python packages"""
    print("\n🐍 Installing Python dependencies...")
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    
    # Install packages
    packages = [
        "crawl4ai>=0.6.0",
        "firecrawl-py>=0.0.16",
        "supabase>=2.0.0",
        "openai>=1.0.0",
        "anthropic>=0.25.0",
        "PyPDF2>=3.0.0",
        "python-docx>=0.8.11",
        "Pillow>=10.0.0",
        "pytesseract>=0.3.10",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "selenium>=4.15.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.0.0",
        "aiofiles>=23.0.0"
    ]
    
    for package in packages:
        run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
    
    # Install Playwright browsers
    run_command(f"{sys.executable} -m playwright install chromium", "Installing Playwright browsers")

def setup_environment():
    """Set up environment configuration"""
    print("\n⚙️ Setting up environment...")
    
    # Create .env file if it doesn't exist
    if not Path('.env').exists():
        if Path('.env.example').exists():
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
            print("📝 Please edit .env file with your API keys:")
            print("   - SUPABASE_URL and SUPABASE_KEY")
            print("   - FIRECRAWL_API_KEY")
            print("   - OPENAI_API_KEY or ANTHROPIC_API_KEY")
        else:
            print("❌ .env.example not found")
    else:
        print("✅ .env file already exists")
    
    # Create output directories
    directories = ['scraper_output', 'scraper_output/screenshots', 'scraper_output/documents']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ Created output directories")

def test_installation():
    """Test if everything is installed correctly"""
    print("\n🧪 Testing installation...")
    
    # Test Python imports
    test_imports = [
        ('crawl4ai', 'Crawl4AI'),
        ('firecrawl', 'FireCrawl'),
        ('supabase', 'Supabase'),
        ('openai', 'OpenAI'),
        ('anthropic', 'Anthropic'),
        ('PyPDF2', 'PyPDF2'),
        ('docx', 'python-docx'),
        ('PIL', 'Pillow'),
        ('pytesseract', 'pytesseract')
    ]
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} imported successfully")
        except ImportError as e:
            print(f"❌ {name} import failed: {e}")
    
    # Test tesseract
    if shutil.which('tesseract'):
        print("✅ Tesseract OCR found")
    else:
        print("❌ Tesseract OCR not found in PATH")
    
    # Test environment file
    if Path('.env').exists():
        print("✅ Environment file exists")
    else:
        print("❌ Environment file missing")

def display_next_steps():
    """Display next steps for the user"""
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API keys")
    print("2. Test the scraper: python3 super_hybrid_scraper.py")
    print("3. Read the guide: SUPER_HYBRID_GUIDE.md")
    print("\n🔑 Required API keys:")
    print("   • Supabase: https://supabase.com/ (free tier available)")
    print("   • FireCrawl: https://firecrawl.dev/ (500 free requests/month)")
    print("   • OpenAI: https://platform.openai.com/ (pay-per-use)")
    print("   • Anthropic: https://console.anthropic.com/ (pay-per-use)")
    print("\n📚 Documentation:")
    print("   • Super Hybrid Guide: SUPER_HYBRID_GUIDE.md")
    print("   • FireCrawl Docs: https://docs.firecrawl.dev/")
    print("   • Crawl4AI Docs: https://crawl4ai.com/")

def main():
    """Main setup function"""
    print("🚀 Super Hybrid CIDB Scraper Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install system dependencies
    install_system_dependencies()
    
    # Install Python dependencies
    install_python_dependencies()
    
    # Setup environment
    setup_environment()
    
    # Test installation
    test_installation()
    
    # Display next steps
    display_next_steps()
    
    print("\n✨ Setup complete! Happy scraping! ✨")

if __name__ == "__main__":
    main()
