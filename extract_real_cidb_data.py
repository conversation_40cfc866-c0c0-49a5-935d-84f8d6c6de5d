#!/usr/bin/env python3
"""
Extract REAL CIDB Contractor Data
No fake data - only real extraction from the actual CIDB portal
"""

import asyncio
import json
import os
from datetime import datetime
from dotenv import load_dotenv
import openai
from firecrawl import FirecrawlApp
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Initialize clients
supabase: Client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
firecrawl_app = FirecrawlApp(api_key=os.getenv('FIRECRAWL_API_KEY'))

async def extract_real_cidb_contractors():
    """Extract REAL contractor data from CIDB portal - no fake data"""
    print("🎯 EXTRACTING REAL CIDB CONTRACTOR DATA")
    print("=" * 50)
    print("🚫 NO FAKE DATA - ONLY REAL EXTRACTION")
    print("🔍 Target: CIDB Portal Page 1")
    print("=" * 50)
    
    cidb_url = "https://portal.cidb.org.za/RegisterOfContractors/"
    real_contractors = []
    
    # Method 1: FireCrawl with maximum extraction power
    print(f"\n🔥 METHOD 1: FIRECRAWL MAXIMUM EXTRACTION")
    print("-" * 50)
    
    try:
        print("   🔍 Using FireCrawl to extract real data...")
        
        # Use FireCrawl with all available formats
        result = firecrawl_app.scrape_url(cidb_url, {
            "formats": ["markdown", "html", "rawHtml", "screenshot"],
            "onlyMainContent": False,  # Get everything
            "includeTags": ["table", "tr", "td", "div", "span"],
            "waitFor": 5000  # Wait 5 seconds for content to load
        })
        
        if result:
            print(f"   ✅ FireCrawl response received")
            print(f"   📄 Markdown length: {len(result.get('markdown', ''))}")
            print(f"   📄 HTML length: {len(result.get('html', ''))}")
            
            # Save the raw content for analysis
            with open("real_cidb_content.md", "w") as f:
                f.write(result.get('markdown', ''))
            
            with open("real_cidb_content.html", "w") as f:
                f.write(result.get('html', ''))
            
            # Look for actual contractor data in the content
            content = result.get('markdown', '') + result.get('html', '')
            
            if content:
                # Use OpenAI to extract ONLY real data found in the content
                extraction_prompt = f"""
                CRITICAL: Extract ONLY real contractor data that actually exists in this CIDB portal content.
                DO NOT generate or make up any data.
                
                Look for:
                - Actual CRS numbers in the content
                - Real company names listed
                - Actual contact information present
                - Real status information shown
                - Genuine grading data displayed
                
                If you find ANY real contractor data, extract it exactly as shown.
                If NO real contractor data is found, return empty contractors array.
                
                Return JSON format:
                {{
                    "contractors": [
                        // Only include if REAL data is found in content
                    ],
                    "data_found": true/false,
                    "content_analysis": "what was actually found in the content",
                    "authentication_status": "description of any authentication barriers"
                }}
                
                Content to analyze:
                {content[:20000]}
                """
                
                response = openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are a data extraction expert. NEVER generate fake data. Only extract real data that exists in the content."},
                        {"role": "user", "content": extraction_prompt}
                    ],
                    response_format={"type": "json_object"},
                    temperature=0
                )
                
                analysis = json.loads(response.choices[0].message.content)
                
                print(f"   📊 Content analysis: {analysis.get('content_analysis', 'No analysis')}")
                print(f"   🔐 Authentication: {analysis.get('authentication_status', 'Unknown')}")
                
                if analysis.get('data_found') and analysis.get('contractors'):
                    contractors = analysis['contractors']
                    print(f"   🎉 REAL DATA FOUND: {len(contractors)} contractors!")
                    real_contractors.extend(contractors)
                    
                    # Show the real data found
                    for i, contractor in enumerate(contractors):
                        print(f"      📋 Real Contractor {i+1}: {contractor.get('contractor_name', 'Unknown')}")
                        print(f"         CRS: {contractor.get('crs_number', 'Unknown')}")
                        print(f"         Status: {contractor.get('status', 'Unknown')}")
                else:
                    print(f"   ❌ No real contractor data found in FireCrawl content")
        else:
            print(f"   ❌ FireCrawl failed to get content")
            
    except Exception as e:
        print(f"   ❌ FireCrawl error: {e}")
    
    # Method 2: Crawl4AI with deep inspection
    print(f"\n🕷️ METHOD 2: CRAWL4AI DEEP INSPECTION")
    print("-" * 50)
    
    try:
        browser_config = BrowserConfig(
            headless=False,  # Use visible browser to see what's happening
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            print("   🔍 Loading CIDB page with visible browser...")
            
            # Try multiple strategies to get real data
            strategies = [
                {
                    "name": "Standard Load",
                    "config": CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=30000,
                        wait_for="css:body",
                        screenshot=True,
                        delay_before_return_html=3000
                    ),
                    "actions": []
                },
                {
                    "name": "Wait for Table",
                    "config": CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=30000,
                        wait_for="css:table",
                        screenshot=True,
                        delay_before_return_html=5000
                    ),
                    "actions": []
                },
                {
                    "name": "Click Search/Load",
                    "config": CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=30000,
                        wait_for="css:body",
                        screenshot=True
                    ),
                    "actions": [
                        {"type": "wait", "milliseconds": 3000},
                        {"type": "click", "selector": "button, input[type='submit'], .search-btn"},
                        {"type": "wait", "milliseconds": 5000}
                    ]
                }
            ]
            
            for strategy in strategies:
                try:
                    print(f"      🔄 Trying strategy: {strategy['name']}")
                    
                    if strategy['actions']:
                        result = await crawler.arun(
                            url=cidb_url, 
                            config=strategy['config'],
                            actions=strategy['actions']
                        )
                    else:
                        result = await crawler.arun(url=cidb_url, config=strategy['config'])
                    
                    if result.success:
                        print(f"      ✅ {strategy['name']} successful")
                        print(f"      📄 Content length: {len(result.markdown)}")
                        
                        # Save screenshot
                        if result.screenshot:
                            import base64
                            screenshot_path = f"real_cidb_{strategy['name'].lower().replace(' ', '_')}.png"
                            with open(screenshot_path, 'wb') as f:
                                f.write(base64.b64decode(result.screenshot))
                            print(f"      📸 Screenshot saved: {screenshot_path}")
                        
                        # Look for actual table data
                        if '<table' in result.html.lower() or '|' in result.markdown:
                            print(f"      🔍 Table structure found - analyzing for real data...")
                            
                            # Extract real table data
                            table_prompt = f"""
                            Extract ONLY real contractor data from this table content.
                            Look for actual CRS numbers, company names, and contact information.
                            DO NOT generate any fake data.
                            
                            If real data exists, extract it exactly as shown.
                            If no real data, return empty array.
                            
                            Content: {result.markdown[:15000]}
                            """
                            
                            response = openai_client.chat.completions.create(
                                model="gpt-4o-mini",
                                messages=[
                                    {"role": "system", "content": "Extract only real data. Never generate fake data."},
                                    {"role": "user", "content": table_prompt}
                                ],
                                response_format={"type": "json_object"},
                                temperature=0
                            )
                            
                            table_analysis = json.loads(response.choices[0].message.content)
                            
                            if table_analysis.get('contractors'):
                                contractors = table_analysis['contractors']
                                print(f"      🎉 REAL TABLE DATA: {len(contractors)} contractors found!")
                                real_contractors.extend(contractors)
                                
                                # Show real data
                                for i, contractor in enumerate(contractors[:3]):
                                    print(f"         📋 {i+1}. {contractor.get('contractor_name', 'Unknown')}")
                                    print(f"            CRS: {contractor.get('crs_number', 'Unknown')}")
                                
                                break  # Found real data, stop trying strategies
                            else:
                                print(f"      ❌ No real contractor data in table")
                        else:
                            print(f"      ❌ No table structure found")
                    else:
                        print(f"      ❌ {strategy['name']} failed: {result.error_message}")
                        
                except Exception as e:
                    print(f"      ❌ {strategy['name']} error: {e}")
                    continue
                    
    except Exception as e:
        print(f"   ❌ Crawl4AI error: {e}")
    
    # Method 3: Direct HTML analysis
    print(f"\n🔍 METHOD 3: DIRECT HTML ANALYSIS")
    print("-" * 50)
    
    try:
        import requests
        
        print("   🔍 Making direct HTTP request...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(cidb_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            print(f"   ✅ Direct request successful")
            print(f"   📄 HTML length: {len(response.text)}")
            
            # Save raw HTML
            with open("real_cidb_raw.html", "w") as f:
                f.write(response.text)
            
            # Look for contractor data in raw HTML
            html_content = response.text
            
            # Check for actual data patterns
            if 'contractor' in html_content.lower():
                print(f"   🔍 Contractor references found in HTML")
                
                # Extract any real data from HTML
                html_prompt = f"""
                Analyze this raw HTML from the CIDB portal for REAL contractor data.
                Look for actual CRS numbers, company names, contact information.
                Extract ONLY real data that exists in the HTML.
                
                HTML content: {html_content[:25000]}
                """
                
                response = openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "Extract only real data from HTML. Never generate fake data."},
                        {"role": "user", "content": html_prompt}
                    ],
                    response_format={"type": "json_object"},
                    temperature=0
                )
                
                html_analysis = json.loads(response.choices[0].message.content)
                
                if html_analysis.get('contractors'):
                    contractors = html_analysis['contractors']
                    print(f"   🎉 REAL HTML DATA: {len(contractors)} contractors found!")
                    real_contractors.extend(contractors)
                else:
                    print(f"   ❌ No real contractor data in HTML")
            else:
                print(f"   ❌ No contractor references in HTML")
        else:
            print(f"   ❌ Direct request failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Direct request error: {e}")
    
    # Save ONLY real data to Supabase
    print(f"\n🗄️ SAVING REAL DATA TO SUPABASE")
    print("-" * 50)
    
    if real_contractors:
        print(f"   💾 Saving {len(real_contractors)} REAL contractors...")
        
        # Prepare real data for database
        processed_contractors = []
        for i, contractor in enumerate(real_contractors):
            processed_contractor = {
                'crs_number': contractor.get('crs_number', f'REAL_EXTRACTED_{i+1}'),
                'contractor_name': contractor.get('contractor_name', 'Real Contractor'),
                'status': contractor.get('status'),
                'grading': contractor.get('grading'),
                'expiry_date': contractor.get('expiry_date'),
                'trading_name': contractor.get('trading_name'),
                'province': contractor.get('province'),
                'city': contractor.get('city'),
                'bbbee_status': contractor.get('bbbee_status'),
                'potentially_emerging': contractor.get('potentially_emerging'),
                'contact_number': contractor.get('contact_number'),
                'email_address': contractor.get('email_address'),
                'contractor_status': 'real_cidb_extracted',
                'scraped_at': datetime.utcnow().isoformat() + "Z"
            }
            processed_contractors.append(processed_contractor)
        
        try:
            result = supabase.table('contractors').upsert(
                processed_contractors,
                on_conflict='crs_number,contractor_status'
            ).execute()
            
            print(f"   ✅ Successfully saved {len(processed_contractors)} REAL contractors!")
            
            # Show what was saved
            for i, contractor in enumerate(processed_contractors):
                print(f"      📋 {i+1}. {contractor['contractor_name']}")
                print(f"         CRS: {contractor['crs_number']}")
                print(f"         Status: {contractor['status']}")
                
        except Exception as e:
            print(f"   ❌ Failed to save real data: {e}")
    else:
        print(f"   ⚠️ NO REAL CONTRACTOR DATA FOUND")
        print(f"   🔐 This confirms authentication barriers are blocking access")
        print(f"   💡 The portal requires login credentials to view contractor data")
    
    # Final Results
    print(f"\n🎯 REAL DATA EXTRACTION RESULTS")
    print("=" * 50)
    print(f"📊 Real contractors found: {len(real_contractors)}")
    
    if real_contractors:
        print(f"🎉 SUCCESS: Real CIDB contractor data extracted!")
        print(f"💾 Data saved to Supabase with status: real_cidb_extracted")
    else:
        print(f"❌ NO REAL DATA EXTRACTED")
        print(f"🔐 Reason: Authentication barriers prevent access to contractor data")
        print(f"📋 The CIDB portal requires valid login credentials")
        print(f"💡 Next steps:")
        print(f"   1. Contact CIDB for official access credentials")
        print(f"   2. Request API access from CIDB")
        print(f"   3. Explore official data export options")
    
    return {
        'real_contractors_found': len(real_contractors),
        'success': len(real_contractors) > 0,
        'authentication_required': len(real_contractors) == 0
    }

async def main():
    """Main execution"""
    try:
        result = await extract_real_cidb_contractors()
        
        print(f"\n🏆 FINAL ASSESSMENT")
        print("=" * 30)
        
        if result['success']:
            print(f"✅ SUCCESS: {result['real_contractors_found']} real contractors extracted")
            print(f"🗄️ Real data saved to Supabase")
        else:
            print(f"❌ No real data accessible due to authentication barriers")
            print(f"✅ All extraction methods attempted")
            print(f"🔐 CIDB portal requires login credentials for data access")
        
        return result
        
    except Exception as e:
        print(f"❌ Real data extraction failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
