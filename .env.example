# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-service-role-key

# FireCrawl API Configuration
FIRECRAWL_API_KEY=fc-your-firecrawl-api-key

# LLM API Keys (at least one required for LLM extraction)
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# Optional: Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=super_hybrid_scraper.log

# Optional: Scraping Configuration
MAX_RETRIES=3
DELAY_BETWEEN_PAGES=2
BATCH_SIZE=100
FIRECRAWL_FREE_LIMIT=500
RATE_LIMIT_DELAY=2

# Optional: OCR Configuration (for screenshot analysis)
TESSERACT_CMD=/usr/bin/tesseract  # Path to tesseract executable
