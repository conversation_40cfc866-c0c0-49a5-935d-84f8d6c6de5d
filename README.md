# CIDB Contractor Scraper

A comprehensive web scraper built with Crawl4AI framework to extract contractor data from the CIDB (Construction Industry Development Board) portal and store it in Supabase.

## 🚀 Features

- **Modern Framework**: Built with Crawl4AI for robust web scraping
- **Database Integration**: Stores data in Supabase PostgreSQL database
- **Comprehensive Logging**: Detailed logging and error tracking
- **Batch Processing**: Efficient batch processing with pagination support
- **Data Validation**: Clean and validate scraped data
- **Statistics Tracking**: Track scraping runs and performance metrics
- **Deployment Ready**: Includes deployment scripts and systemd service configuration

## 📋 Prerequisites

- Python 3.8 or higher
- Supabase account and project
- Chrome/Chromium browser (installed automatically via Playwright)

## 🛠 Installation

1. **Clone or download the project files**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers:**
   ```bash
   python -m playwright install chromium
   ```

4. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-service-role-key

# Optional: Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=cidb_scraper.log

# Optional: Scraping Configuration
MAX_RETRIES=3
DELAY_BETWEEN_PAGES=2
BATCH_SIZE=100
```

### Database Schema

The scraper creates two main tables:

1. **contractors** - Stores contractor information
2. **scraping_runs** - Tracks scraping sessions and statistics

## 🏃‍♂️ Usage

### Run Tests
```bash
python test_scraper.py
```

### Test Extraction (Limited)
```bash
python test_extraction.py
```

### Full Scrape
```bash
python cidb_scraper.py
```

### Automated Deployment
```bash
chmod +x deploy.sh
./deploy.sh
```

## 📊 Data Structure

### Contractors Table
- `crs_number` - Contractor registration number
- `contractor_name` - Company name
- `status` - Registration status
- `grading` - CIDB grading level
- `expiry_date` - Registration expiry date
- `trading_name` - Trading name
- `province` - Province location
- `city` - City location
- `bbbee_status` - B-BBEE status
- `potentially_emerging` - Emerging contractor flag
- `contractor_status` - Active/Inactive
- `scraped_at` - Timestamp of data extraction

### Scraping Runs Table
- `run_id` - Unique run identifier
- `status` - Run status (started/completed/failed/partial)
- `total_pages_scraped` - Number of pages processed
- `total_contractors_found` - Total contractors extracted
- `start_time` / `end_time` - Run timing
- `errors_encountered` - Any errors during scraping

## 🔧 Architecture

### Core Components

1. **CIDBScraper Class** - Main scraper logic
2. **AsyncWebCrawler** - Crawl4AI browser automation
3. **JsonCssExtractionStrategy** - CSS-based data extraction
4. **Supabase Client** - Database operations
5. **ScrapingStats** - Statistics tracking

### Key Features

- **Async Processing** - Non-blocking operations
- **Error Recovery** - Robust error handling and retry logic
- **Rate Limiting** - Respectful delays between requests
- **Data Validation** - Clean and validate extracted data
- **Batch Operations** - Efficient database operations

## 🚨 Important Notes

### Authentication Requirements

The CIDB portal appears to require authentication or specific permissions to access contractor data. The current implementation may encounter:

- "You don't have permissions to view these records"
- "There are no records to display"

### Potential Solutions

1. **Authentication Integration**
   - Add login functionality
   - Handle session management
   - Implement cookie persistence

2. **API Access**
   - Check if CIDB provides an official API
   - Use authenticated API endpoints

3. **Manual Data Access**
   - Obtain proper credentials
   - Use authorized access methods

## 📈 Monitoring

### Logs
- Application logs: `cidb_scraper.log`
- Cron logs: `cron.log` (if using cron)

### Database Monitoring
Check the `scraping_runs` table in Supabase for:
- Run success/failure rates
- Performance metrics
- Error patterns

### System Monitoring
```bash
# Check systemd service status
sudo systemctl status cidb-scraper.service

# Check timer status
sudo systemctl status cidb-scraper.timer

# View logs
journalctl -u cidb-scraper.service -f
```

## 🔄 Scheduling

### Systemd Timer (Recommended)
The deployment script can create a systemd timer for daily execution:
```bash
sudo systemctl enable cidb-scraper.timer
sudo systemctl start cidb-scraper.timer
```

### Cron Job (Alternative)
```bash
# Daily at 2 AM
0 2 * * * cd /path/to/scraper && python cidb_scraper.py
```

## 🛠 Troubleshooting

### Common Issues

1. **Browser Installation**
   ```bash
   python -m playwright install chromium
   ```

2. **Permission Errors**
   - Check Supabase credentials
   - Verify database permissions
   - Ensure proper environment variables

3. **Network Issues**
   - Check internet connectivity
   - Verify CIDB portal accessibility
   - Consider proxy settings if needed

### Debug Mode
Enable verbose logging by setting `LOG_LEVEL=DEBUG` in `.env`

## 📝 Development

### Project Structure
```
├── cidb_scraper.py          # Main scraper implementation
├── test_scraper.py          # Test suite
├── test_extraction.py       # Extraction testing
├── inspect_cidb.py          # Site inspection utility
├── requirements.txt         # Python dependencies
├── .env.example            # Environment template
├── deploy.sh               # Deployment script
└── README.md               # This file
```

### Contributing
1. Test changes with `python test_scraper.py`
2. Ensure proper error handling
3. Update documentation as needed
4. Follow existing code style

## 📄 License

This project is for educational and research purposes. Please ensure compliance with CIDB's terms of service and robots.txt when using this scraper.

## 🤝 Support

For issues or questions:
1. Check the logs for error details
2. Verify configuration settings
3. Test with the provided test scripts
4. Review the troubleshooting section

---

**Note**: This scraper is designed to work with the CIDB portal structure as of 2025. Website changes may require updates to the extraction logic.
