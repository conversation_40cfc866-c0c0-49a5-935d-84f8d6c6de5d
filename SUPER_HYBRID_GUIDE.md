# 🚀 Super Hybrid CIDB Scraper - Complete Guide

## 🎯 Overview

The Super Hybrid Scraper is an advanced, multi-method scraping system that combines:

1. **🔥 FireCrawl API** - Professional web scraping with LLM extraction
2. **🕷️ Crawl4AI** - Advanced browser automation and extraction
3. **🤖 LLM Analysis** - OpenAI GPT-4 and Anthropic Claude for intelligent data extraction
4. **📸 Screenshot Analysis** - OCR + LLM analysis of visual content
5. **📄 Document Processing** - PDF, DOCX, and other file format support

## 🛠️ Installation & Setup

### 1. Install Dependencies

```bash
# Install Python packages
pip install -r requirements.txt

# Install Playwright browsers (for Crawl4AI)
python -m playwright install chromium

# Install Tesseract OCR (for screenshot analysis)
# Ubuntu/Debian:
sudo apt-get install tesseract-ocr

# macOS:
brew install tesseract

# Windows:
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
```

### 2. Configure API Keys

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys:
nano .env
```

Required API keys:
- **Supabase**: Database storage (free tier available)
- **FireCrawl**: Professional scraping (500 free requests/month)
- **OpenAI** or **Anthropic**: LLM extraction (choose one or both)

### 3. Database Setup

The scraper automatically uses the existing Supabase database with enhanced contractor fields including contact numbers and email addresses.

## 🚀 Usage Examples

### Basic Usage

```python
from super_hybrid_scraper import SuperHybridScraper
import asyncio

async def basic_scrape():
    scraper = SuperHybridScraper()
    
    # Scrape default CIDB URLs
    result = await scraper.run_super_hybrid_scrape()
    print(f"Found {result['total_contractors']} contractors")

asyncio.run(basic_scrape())
```

### Advanced Usage with Custom Targets

```python
async def advanced_scrape():
    scraper = SuperHybridScraper()
    
    # Mix of URLs and files
    targets = [
        "https://portal.cidb.org.za/RegisterOfContractors/",
        "path/to/contractor_list.pdf",
        "screenshot_of_contractors.png",
        "contractor_data.docx"
    ]
    
    result = await scraper.run_super_hybrid_scrape(targets)
    
    # Detailed results
    for method_result in result['results']:
        print(f"Method: {method_result['methods_used']}")
        print(f"Contractors found: {method_result['total_contractors']}")

asyncio.run(advanced_scrape())
```

### File Processing Only

```python
async def process_documents():
    scraper = SuperHybridScraper()
    
    # Process various file types
    pdf_result = await scraper.process_pdf("contractor_report.pdf")
    docx_result = await scraper.process_docx("contractor_list.docx")
    screenshot_result = await scraper.screenshot_analysis("cidb_screenshot.png")
    
    print(f"PDF: {len(pdf_result['contractors'])} contractors")
    print(f"DOCX: {len(docx_result['contractors'])} contractors")
    print(f"Screenshot: {len(screenshot_result['contractors'])} contractors")

asyncio.run(process_documents())
```

## 🔧 Configuration Options

### Rate Limiting

The scraper respects FireCrawl's free tier limits:

```python
# In .env file
FIRECRAWL_FREE_LIMIT=500  # Requests per month
RATE_LIMIT_DELAY=2        # Seconds between requests
```

### LLM Configuration

Choose your preferred LLM provider:

```python
# OpenAI (recommended for structured extraction)
OPENAI_API_KEY=sk-your-key

# Anthropic Claude (good for complex analysis)
ANTHROPIC_API_KEY=sk-ant-your-key

# Both can be used - scraper will try OpenAI first, then Claude
```

### OCR Configuration

For screenshot analysis:

```python
# Path to tesseract executable
TESSERACT_CMD=/usr/bin/tesseract

# OCR language (default: English)
OCR_LANG=eng
```

## 📊 Monitoring & Analytics

### Real-time Statistics

The scraper tracks comprehensive statistics:

```python
# Access stats during or after scraping
print(f"FireCrawl requests: {scraper.stats.firecrawl_requests}")
print(f"Crawl4AI requests: {scraper.stats.crawl4ai_requests}")
print(f"LLM requests: {scraper.stats.llm_requests}")
print(f"Screenshots analyzed: {scraper.stats.screenshot_analyses}")
print(f"PDFs processed: {scraper.stats.pdf_processed}")
print(f"Rate limit hits: {scraper.stats.rate_limit_hits}")
```

### Database Monitoring

Check results in Supabase:

```sql
-- View scraping runs
SELECT * FROM scraping_runs ORDER BY start_time DESC;

-- View contractors by source
SELECT contractor_status, COUNT(*) 
FROM contractors 
GROUP BY contractor_status;

-- View recent contractors
SELECT * FROM contractors 
WHERE scraped_at > NOW() - INTERVAL '1 day'
ORDER BY scraped_at DESC;
```

## 🎯 Extraction Methods Explained

### 1. FireCrawl API Method

**Best for**: Professional, reliable scraping with built-in LLM extraction

```python
# Automatic schema-based extraction
firecrawl_result = await scraper.firecrawl_scrape(
    url, 
    ContractorList.model_json_schema()
)
```

**Advantages**:
- ✅ Handles JavaScript-heavy sites
- ✅ Built-in LLM extraction
- ✅ Professional reliability
- ✅ Automatic screenshot capture

**Rate Limits**: 500 requests/month (free tier)

### 2. Crawl4AI Method

**Best for**: Advanced browser automation and custom extraction

```python
# CSS selector-based extraction
crawl4ai_result = await scraper.crawl4ai_scrape(url)
```

**Advantages**:
- ✅ Highly customizable
- ✅ Advanced browser automation
- ✅ No API costs
- ✅ Complex interaction support

**Rate Limits**: None (local processing)

### 3. LLM Analysis Method

**Best for**: Intelligent content understanding and extraction

```python
# Analyze any text content with LLM
llm_result = await scraper.llm_extract_from_content(content, "html")
```

**Advantages**:
- ✅ Understands context and meaning
- ✅ Handles unstructured data
- ✅ Flexible extraction patterns
- ✅ High accuracy for complex data

**Rate Limits**: Based on LLM provider (OpenAI/Anthropic)

### 4. Screenshot Analysis Method

**Best for**: Visual content and protected/dynamic pages

```python
# OCR + LLM analysis of screenshots
screenshot_result = await scraper.screenshot_analysis("screenshot.png")
```

**Advantages**:
- ✅ Works with any visual content
- ✅ Bypasses some protection mechanisms
- ✅ Handles dynamic/JavaScript content
- ✅ Good for manual verification

**Rate Limits**: Local OCR + LLM API calls

### 5. Document Processing Method

**Best for**: PDF reports, DOCX files, and document archives

```python
# Extract from various document formats
pdf_result = await scraper.process_pdf("report.pdf")
docx_result = await scraper.process_docx("list.docx")
```

**Advantages**:
- ✅ Handles multiple file formats
- ✅ Extracts tables and structured data
- ✅ Good for official reports
- ✅ Batch processing support

**Rate Limits**: Local processing + LLM API calls

## 🔄 Workflow Recommendations

### For Maximum Coverage

1. **Start with FireCrawl** - Most reliable for web scraping
2. **Add Crawl4AI** - Backup method and custom extraction
3. **Use LLM Analysis** - Intelligent processing of all content
4. **Include Screenshot Analysis** - Visual verification and backup
5. **Process Documents** - Handle any PDF/DOCX files

### For Cost Optimization

1. **Use Crawl4AI first** - No API costs
2. **Add LLM analysis** - Only for complex content
3. **Use FireCrawl sparingly** - For difficult sites only
4. **Screenshot analysis** - As last resort

### For Speed Optimization

1. **Parallel processing** - Run multiple methods simultaneously
2. **Batch operations** - Process multiple targets together
3. **Smart rate limiting** - Optimize API usage
4. **Caching** - Store and reuse results

## 🚨 Troubleshooting

### Common Issues

1. **Rate Limit Exceeded**
   ```
   Solution: Increase RATE_LIMIT_DELAY or wait for reset
   ```

2. **LLM API Errors**
   ```
   Solution: Check API keys and quotas
   ```

3. **OCR Not Working**
   ```
   Solution: Install tesseract-ocr properly
   ```

4. **Database Connection Issues**
   ```
   Solution: Verify Supabase credentials
   ```

### Debug Mode

Enable verbose logging:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## 📈 Performance Optimization

### Batch Processing

```python
# Process multiple targets efficiently
targets = [
    "url1", "url2", "file1.pdf", "file2.docx"
]
result = await scraper.run_super_hybrid_scrape(targets)
```

### Parallel Execution

```python
# Run multiple scrapers in parallel
import asyncio

async def parallel_scraping():
    scrapers = [SuperHybridScraper() for _ in range(3)]
    tasks = [
        scraper.hybrid_scrape_url(url) 
        for scraper, url in zip(scrapers, urls)
    ]
    results = await asyncio.gather(*tasks)

asyncio.run(parallel_scraping())
```

## 🎉 Success Metrics

The scraper is successful when:

- ✅ Multiple extraction methods work together
- ✅ Rate limits are respected
- ✅ Data quality is high (validated schemas)
- ✅ All file formats are supported
- ✅ Comprehensive error handling works
- ✅ Database storage is reliable
- ✅ Statistics tracking is accurate

## 🔮 Future Enhancements

Planned improvements:
- 🔄 Auto-retry mechanisms
- 🎯 Smart target discovery
- 📊 Advanced analytics dashboard
- 🔐 Enhanced authentication handling
- 🌐 Multi-language support
- 📱 Mobile-responsive extraction
