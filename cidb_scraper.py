#!/usr/bin/env python3
"""
CIDB Contractor Scraper using Crawl4AI
Scrapes contractor data from CIDB portal and stores in Supabase
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
CIDB_BASE_URL = "https://portal.cidb.org.za/RegisterOfContractors/"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cidb_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingStats:
    """Track scraping statistics"""
    run_id: str
    total_pages: int = 0
    total_contractors: int = 0
    active_contractors: int = 0
    inactive_contractors: int = 0
    new_contractors: int = 0
    updated_contractors: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class CIDBScraper:
    """CIDB Contractor Scraper using Crawl4AI"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )
        
        # CSS extraction schema for contractor data
        self.extraction_schema = {
            "name": "CIDB Contractors",
            "baseSelector": "#ctl00_ContentPlaceHolder1_gvContractors tr:not(:first-child)",
            "fields": [
                {
                    "name": "crs_number",
                    "selector": "td:nth-child(1)",
                    "type": "text"
                },
                {
                    "name": "contractor_name", 
                    "selector": "td:nth-child(2)",
                    "type": "text"
                },
                {
                    "name": "status",
                    "selector": "td:nth-child(3)", 
                    "type": "text"
                },
                {
                    "name": "grading",
                    "selector": "td:nth-child(4)",
                    "type": "text"
                },
                {
                    "name": "expiry_date",
                    "selector": "td:nth-child(5)",
                    "type": "text"
                },
                {
                    "name": "trading_name",
                    "selector": "td:nth-child(6)",
                    "type": "text"
                },
                {
                    "name": "province",
                    "selector": "td:nth-child(7)",
                    "type": "text"
                },
                {
                    "name": "city",
                    "selector": "td:nth-child(8)",
                    "type": "text"
                },
                {
                    "name": "bbbee_status",
                    "selector": "td:nth-child(9)",
                    "type": "text"
                },
                {
                    "name": "potentially_emerging",
                    "selector": "td:nth-child(10)",
                    "type": "text"
                }
            ]
        }
    
    async def initialize_scraping_run(self, contractor_type: str) -> str:
        """Initialize a new scraping run in the database"""
        try:
            result = supabase.table('scraping_runs').insert({
                'run_id': self.stats.run_id,
                'status': 'started',
                'contractor_type': contractor_type,
                'scraper_version': '1.0.0',
                'user_agent': self.browser_config.user_agent
            }).execute()
            
            logger.info(f"Initialized scraping run: {self.stats.run_id}")
            return self.stats.run_id
            
        except Exception as e:
            logger.error(f"Failed to initialize scraping run: {str(e)}")
            raise
    
    async def update_scraping_run(self, status: str, end_time: Optional[datetime] = None):
        """Update scraping run status and statistics"""
        try:
            update_data = {
                'status': status,
                'total_pages_scraped': self.stats.total_pages,
                'total_contractors_found': self.stats.total_contractors,
                'active_contractors_count': self.stats.active_contractors,
                'inactive_contractors_count': self.stats.inactive_contractors,
                'new_contractors_added': self.stats.new_contractors,
                'updated_contractors_count': self.stats.updated_contractors,
                'errors_encountered': self.stats.errors
            }
            
            if end_time:
                update_data['end_time'] = end_time.isoformat()
            
            supabase.table('scraping_runs').update(update_data).eq('run_id', self.stats.run_id).execute()
            logger.info(f"Updated scraping run {self.stats.run_id} with status: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update scraping run: {str(e)}")
    
    async def set_contractor_status(self, crawler: AsyncWebCrawler, status: str) -> bool:
        """Set the contractor status (active/inactive) on the CIDB portal"""
        try:
            # JavaScript to click the appropriate radio button
            radio_button_id = "ctl00_ContentPlaceHolder1_rbActive" if status == "active" else "ctl00_ContentPlaceHolder1_rbInactive"
            
            js_code = f"""
            (async () => {{
                const radio = document.getElementById('{radio_button_id}');
                if (radio) {{
                    radio.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    return true;
                }}
                return false;
            }})();
            """
            
            config = CrawlerRunConfig(
                js_code=[js_code],
                cache_mode=CacheMode.BYPASS,
                wait_for="css:#ctl00_ContentPlaceHolder1_gvContractors"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            return result.success
            
        except Exception as e:
            logger.error(f"Failed to set contractor status to {status}: {str(e)}")
            self.stats.errors.append(f"Status setting error: {str(e)}")
            return False
    
    async def set_page_size(self, crawler: AsyncWebCrawler) -> bool:
        """Set page size to 100 records per page"""
        try:
            js_code = """
            (async () => {
                const pageSize = document.getElementById('ctl00_ContentPlaceHolder1_ddlPageSize');
                if (pageSize) {
                    pageSize.value = '100';
                    pageSize.dispatchEvent(new Event('change'));
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    return true;
                }
                return false;
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[js_code],
                cache_mode=CacheMode.BYPASS,
                wait_for="css:#ctl00_ContentPlaceHolder1_gvContractors"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            return result.success
            
        except Exception as e:
            logger.error(f"Failed to set page size: {str(e)}")
            self.stats.errors.append(f"Page size error: {str(e)}")
            return False
    
    async def scrape_page(self, crawler: AsyncWebCrawler, page_num: int = 1) -> List[Dict]:
        """Scrape contractors from current page"""
        try:
            extraction_strategy = JsonCssExtractionStrategy(self.extraction_schema, verbose=False)
            
            config = CrawlerRunConfig(
                extraction_strategy=extraction_strategy,
                cache_mode=CacheMode.BYPASS,
                wait_for="css:#ctl00_ContentPlaceHolder1_gvContractors tr:nth-child(2)"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            
            if not result.success:
                logger.error(f"Failed to scrape page {page_num}: {result.error_message}")
                self.stats.errors.append(f"Page {page_num} scraping failed: {result.error_message}")
                return []
            
            # Parse extracted data
            contractors = json.loads(result.extracted_content) if result.extracted_content else []
            
            # Clean and validate data
            cleaned_contractors = []
            for contractor in contractors:
                if contractor.get('crs_number') and contractor.get('crs_number').strip():
                    # Clean all text fields
                    cleaned_contractor = {
                        key: value.strip() if isinstance(value, str) else value
                        for key, value in contractor.items()
                    }
                    cleaned_contractor['scraped_at'] = datetime.utcnow().isoformat() + "Z"
                    cleaned_contractors.append(cleaned_contractor)
            
            logger.info(f"Scraped {len(cleaned_contractors)} contractors from page {page_num}")
            return cleaned_contractors
            
        except Exception as e:
            logger.error(f"Error scraping page {page_num}: {str(e)}")
            self.stats.errors.append(f"Page {page_num} error: {str(e)}")
            return []
    
    async def has_next_page(self, crawler: AsyncWebCrawler) -> bool:
        """Check if there's a next page available"""
        try:
            js_code = """
            (() => {
                const nextButton = document.getElementById('ctl00_ContentPlaceHolder1_gvContractors_ctl01_lnkbtnNext');
                return nextButton && !nextButton.classList.contains('disabled') && nextButton.style.display !== 'none';
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[js_code],
                cache_mode=CacheMode.BYPASS
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            return result.success and "true" in str(result.js_execution_result)
            
        except Exception as e:
            logger.error(f"Error checking next page: {str(e)}")
            return False
    
    async def go_to_next_page(self, crawler: AsyncWebCrawler) -> bool:
        """Navigate to the next page"""
        try:
            js_code = """
            (async () => {
                const nextButton = document.getElementById('ctl00_ContentPlaceHolder1_gvContractors_ctl01_lnkbtnNext');
                if (nextButton && !nextButton.classList.contains('disabled')) {
                    nextButton.click();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    return true;
                }
                return false;
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[js_code],
                cache_mode=CacheMode.BYPASS,
                wait_for="css:#ctl00_ContentPlaceHolder1_gvContractors tr:nth-child(2)"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            return result.success
            
        except Exception as e:
            logger.error(f"Error navigating to next page: {str(e)}")
            self.stats.errors.append(f"Navigation error: {str(e)}")
            return False

    async def save_contractors_to_supabase(self, contractors: List[Dict], contractor_status: str):
        """Save contractors to Supabase with upsert logic"""
        if not contractors:
            return

        try:
            # Add contractor_status to each record
            for contractor in contractors:
                contractor['contractor_status'] = contractor_status

            # Batch upsert (insert or update based on crs_number + contractor_status)
            batch_size = 100
            for i in range(0, len(contractors), batch_size):
                batch = contractors[i:i+batch_size]

                # Use upsert to handle duplicates
                result = supabase.table('contractors').upsert(
                    batch,
                    on_conflict='crs_number,contractor_status'
                ).execute()

                if hasattr(result, 'error') and result.error:
                    logger.error(f"Supabase upsert error: {result.error}")
                    self.stats.errors.append(f"Database error: {result.error}")
                else:
                    logger.info(f"Upserted {len(batch)} contractors to Supabase")

                    # Update statistics (approximate)
                    self.stats.new_contractors += len(batch)
                    if contractor_status == 'active':
                        self.stats.active_contractors += len(batch)
                    else:
                        self.stats.inactive_contractors += len(batch)

        except Exception as e:
            logger.error(f"Error saving to Supabase: {str(e)}")
            self.stats.errors.append(f"Database save error: {str(e)}")

    async def scrape_contractors_by_status(self, status: str) -> List[Dict]:
        """Scrape all contractors for a given status (active/inactive)"""
        logger.info(f"Starting to scrape {status} contractors...")
        all_contractors = []
        page_num = 1

        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            # Set contractor status
            if not await self.set_contractor_status(crawler, status):
                logger.error(f"Failed to set contractor status to {status}")
                return []

            # Set page size to 100
            if not await self.set_page_size(crawler):
                logger.warning("Failed to set page size, continuing with default")

            # Scrape all pages
            while True:
                logger.info(f"Scraping {status} contractors - Page {page_num}")

                contractors = await self.scrape_page(crawler, page_num)
                if contractors:
                    all_contractors.extend(contractors)
                    self.stats.total_contractors += len(contractors)

                    # Save batch to database
                    await self.save_contractors_to_supabase(contractors, status)

                self.stats.total_pages += 1

                # Check if there's a next page
                if not await self.has_next_page(crawler):
                    logger.info(f"Reached last page for {status} contractors")
                    break

                # Navigate to next page
                if not await self.go_to_next_page(crawler):
                    logger.error(f"Failed to navigate to next page for {status} contractors")
                    break

                page_num += 1

                # Add delay between pages to be respectful
                await asyncio.sleep(2)

        logger.info(f"Completed scraping {status} contractors: {len(all_contractors)} total")
        return all_contractors

    async def run_full_scrape(self):
        """Run complete scraping process for both active and inactive contractors"""
        start_time = datetime.utcnow()

        try:
            # Initialize scraping run
            await self.initialize_scraping_run('both')

            logger.info("Starting CIDB contractor scraping...")

            # Scrape active contractors
            active_contractors = await self.scrape_contractors_by_status('active')

            # Scrape inactive contractors
            inactive_contractors = await self.scrape_contractors_by_status('inactive')

            # Update final statistics
            end_time = datetime.utcnow()

            if self.stats.errors:
                status = 'partial' if (active_contractors or inactive_contractors) else 'failed'
            else:
                status = 'completed'

            await self.update_scraping_run(status, end_time)

            # Log final results
            duration = (end_time - start_time).total_seconds()
            logger.info(f"""
Scraping completed in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Status: {status}
Total Pages: {self.stats.total_pages}
Total Contractors: {self.stats.total_contractors}
Active Contractors: {self.stats.active_contractors}
Inactive Contractors: {self.stats.inactive_contractors}
Errors: {len(self.stats.errors)}
            """)

            if self.stats.errors:
                logger.warning(f"Errors encountered: {self.stats.errors}")

            return {
                'run_id': self.stats.run_id,
                'status': status,
                'total_contractors': self.stats.total_contractors,
                'active_contractors': len(active_contractors),
                'inactive_contractors': len(inactive_contractors),
                'duration_seconds': duration,
                'errors': self.stats.errors
            }

        except Exception as e:
            logger.error(f"Critical error during scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise

async def main():
    """Main entry point"""
    scraper = CIDBScraper()

    try:
        result = await scraper.run_full_scrape()
        print(f"Scraping completed successfully: {result}")
        return result

    except Exception as e:
        logger.error(f"Scraping failed: {str(e)}")
        print(f"Scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
