# Core scraping frameworks
crawl4ai>=0.6.0
firecrawl-py>=0.0.16

# Database and storage
supabase>=2.0.0

# LLM clients
openai>=1.0.0
anthropic>=0.25.0

# Document processing
PyPDF2>=3.0.0
python-docx>=0.8.11
Pillow>=10.0.0
pytesseract>=0.3.10

# Web scraping utilities
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0

# Environment and utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
aiofiles>=23.0.0

# Built-in modules (no installation needed)
# asyncio, logging, dataclasses, uuid, datetime, typing, json, time, base64, mimetypes, pathlib
