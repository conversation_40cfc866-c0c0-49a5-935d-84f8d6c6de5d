#!/usr/bin/env python3
"""
Alternative CIDB Contractor <PERSON><PERSON>er
Uses the public contractor search endpoint found on the main CIDB website
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
CIDB_ALTERNATIVE_URL = "http://registers.cidb.org.za/PublicContractors/ContractorSearch"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('alternative_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingStats:
    """Track scraping statistics"""
    run_id: str
    total_pages: int = 0
    total_contractors: int = 0
    active_contractors: int = 0
    inactive_contractors: int = 0
    new_contractors: int = 0
    updated_contractors: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class AlternativeCIDBScraper:
    """Alternative CIDB Contractor Scraper using public search endpoint"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
    
    async def initialize_scraping_run(self, contractor_type: str) -> str:
        """Initialize a new scraping run in the database"""
        try:
            result = supabase.table('scraping_runs').insert({
                'run_id': self.stats.run_id,
                'status': 'started',
                'contractor_type': contractor_type,
                'scraper_version': '4.0.0',
                'user_agent': self.browser_config.user_agent,
                'notes': 'Alternative scraper using public contractor search endpoint'
            }).execute()
            
            logger.info(f"Initialized scraping run: {self.stats.run_id}")
            return self.stats.run_id
            
        except Exception as e:
            logger.error(f"Failed to initialize scraping run: {str(e)}")
            raise
    
    async def update_scraping_run(self, status: str, end_time: Optional[datetime] = None):
        """Update scraping run status and statistics"""
        try:
            update_data = {
                'status': status,
                'total_pages_scraped': self.stats.total_pages,
                'total_contractors_found': self.stats.total_contractors,
                'active_contractors_count': self.stats.active_contractors,
                'inactive_contractors_count': self.stats.inactive_contractors,
                'new_contractors_added': self.stats.new_contractors,
                'updated_contractors_count': self.stats.updated_contractors,
                'errors_encountered': self.stats.errors
            }
            
            if end_time:
                update_data['end_time'] = end_time.isoformat()
            
            supabase.table('scraping_runs').update(update_data).eq('run_id', self.stats.run_id).execute()
            logger.info(f"Updated scraping run {self.stats.run_id} with status: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update scraping run: {str(e)}")
    
    async def explore_alternative_endpoint(self, crawler: AsyncWebCrawler) -> Dict:
        """Explore the alternative contractor search endpoint"""
        try:
            logger.info("🔍 Exploring alternative CIDB contractor search endpoint...")
            
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                wait_for="css:body"
            )
            
            result = await crawler.arun(url=CIDB_ALTERNATIVE_URL, config=config)
            
            if result.success:
                logger.info("✅ Successfully loaded alternative CIDB endpoint")
                
                # Save the page content for analysis
                with open("alternative_page.html", "w", encoding="utf-8") as f:
                    f.write(result.html)
                
                with open("alternative_page.md", "w", encoding="utf-8") as f:
                    f.write(result.markdown)
                
                # Analyze the page structure
                html_lower = result.html.lower()
                markdown_lower = result.markdown.lower()
                
                elements_found = {
                    'has_table': 'table' in html_lower,
                    'has_contractors': 'contractor' in markdown_lower,
                    'has_form': 'form' in html_lower,
                    'has_search': 'search' in markdown_lower,
                    'has_results': 'results' in markdown_lower,
                    'has_records': 'records' in markdown_lower,
                    'has_permissions_error': 'permissions' in markdown_lower,
                    'has_login': 'login' in html_lower or 'sign in' in html_lower,
                    'has_data_grid': 'grid' in html_lower or 'datagrid' in html_lower,
                    'page_length': len(result.html),
                    'markdown_length': len(result.markdown)
                }
                
                logger.info(f"Alternative endpoint analysis: {elements_found}")
                
                # Check for different error messages
                if 'no records' in markdown_lower:
                    logger.warning("Alternative endpoint shows 'no records'")
                    self.stats.errors.append("No records found on alternative endpoint")
                
                if 'access denied' in markdown_lower or 'unauthorized' in markdown_lower:
                    logger.warning("Alternative endpoint shows access issues")
                    self.stats.errors.append("Access denied on alternative endpoint")
                
                return elements_found
                
            else:
                logger.error(f"Failed to load alternative endpoint: {result.error_message}")
                self.stats.errors.append(f"Alternative endpoint load failed: {result.error_message}")
                return {}
                
        except Exception as e:
            logger.error(f"Error exploring alternative endpoint: {str(e)}")
            self.stats.errors.append(f"Alternative endpoint error: {str(e)}")
            return {}
    
    async def try_search_functionality(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try to use search functionality on the alternative endpoint"""
        contractors = []
        
        try:
            logger.info("🔍 Attempting to use search functionality...")
            
            # Try to perform a search that might return all contractors
            search_js = """
            (async () => {
                try {
                    // Look for search inputs and try different search strategies
                    const searchInputs = document.querySelectorAll('input[type="text"], input[name*="search"], input[id*="search"]');
                    const searchButtons = document.querySelectorAll('input[type="submit"], button[type="submit"], .btn, button');
                    
                    let searchAttempts = [];
                    
                    // Strategy 1: Try empty search (might return all results)
                    if (searchButtons.length > 0) {
                        searchButtons[0].click();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        searchAttempts.push('empty_search_clicked');
                    }
                    
                    // Strategy 2: Try wildcard searches
                    const wildcards = ['*', '%', 'a', ''];
                    for (let wildcard of wildcards) {
                        if (searchInputs.length > 0) {
                            searchInputs[0].value = wildcard;
                            if (searchButtons.length > 0) {
                                searchButtons[0].click();
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                searchAttempts.push(`wildcard_search_${wildcard}`);
                            }
                        }
                    }
                    
                    // Strategy 3: Try to find and click any "Show All" or "List All" buttons
                    const showAllButtons = document.querySelectorAll('*[text*="Show All"], *[text*="List All"], *[value*="All"]');
                    for (let btn of showAllButtons) {
                        try {
                            btn.click();
                            await new Promise(resolve => setTimeout(resolve, 2000));
                            searchAttempts.push('show_all_clicked');
                        } catch (e) {}
                    }
                    
                    return {
                        searchInputsFound: searchInputs.length,
                        searchButtonsFound: searchButtons.length,
                        searchAttempts: searchAttempts,
                        currentUrl: window.location.href
                    };
                } catch (e) {
                    return 'error: ' + e.message;
                }
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[search_js],
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                wait_for="css:body"
            )
            
            result = await crawler.arun(url=CIDB_ALTERNATIVE_URL, config=config)
            
            if result.success:
                logger.info(f"Search functionality result: {result.js_execution_result}")
                
                # Try to extract any contractor data that might have appeared
                contractors = await self.extract_contractor_data_alternative(crawler)
                
        except Exception as e:
            logger.error(f"Search functionality failed: {str(e)}")
            self.stats.errors.append(f"Search functionality error: {str(e)}")
        
        return contractors
    
    async def extract_contractor_data_alternative(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try to extract contractor data from the alternative endpoint"""
        contractors = []
        
        try:
            # Multiple extraction strategies for different table structures
            extraction_schemas = [
                {
                    "name": "Standard Contractor Table",
                    "baseSelector": "table tr:not(:first-child)",
                    "fields": [
                        {"name": "crs_number", "selector": "td:nth-child(1)", "type": "text"},
                        {"name": "contractor_name", "selector": "td:nth-child(2)", "type": "text"},
                        {"name": "status", "selector": "td:nth-child(3)", "type": "text"},
                        {"name": "grading", "selector": "td:nth-child(4)", "type": "text"},
                        {"name": "province", "selector": "td:nth-child(5)", "type": "text"},
                        {"name": "city", "selector": "td:nth-child(6)", "type": "text"}
                    ]
                },
                {
                    "name": "Grid View Data",
                    "baseSelector": ".grid-row, .data-row, [class*='row']",
                    "fields": [
                        {"name": "data_cell_1", "selector": ".cell:nth-child(1), td:nth-child(1)", "type": "text"},
                        {"name": "data_cell_2", "selector": ".cell:nth-child(2), td:nth-child(2)", "type": "text"},
                        {"name": "data_cell_3", "selector": ".cell:nth-child(3), td:nth-child(3)", "type": "text"}
                    ]
                },
                {
                    "name": "Any Table Data",
                    "baseSelector": "tr:has(td)",
                    "fields": [
                        {"name": "col_1", "selector": "td:nth-child(1)", "type": "text"},
                        {"name": "col_2", "selector": "td:nth-child(2)", "type": "text"},
                        {"name": "col_3", "selector": "td:nth-child(3)", "type": "text"},
                        {"name": "col_4", "selector": "td:nth-child(4)", "type": "text"},
                        {"name": "col_5", "selector": "td:nth-child(5)", "type": "text"},
                        {"name": "col_6", "selector": "td:nth-child(6)", "type": "text"}
                    ]
                }
            ]
            
            for schema in extraction_schemas:
                try:
                    extraction_strategy = JsonCssExtractionStrategy(schema, verbose=True)
                    config = CrawlerRunConfig(
                        extraction_strategy=extraction_strategy,
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=15000,
                        wait_for="css:body"
                    )
                    
                    result = await crawler.arun(url=CIDB_ALTERNATIVE_URL, config=config)
                    
                    if result.success and result.extracted_content:
                        extracted_data = json.loads(result.extracted_content)
                        if extracted_data:
                            logger.info(f"✅ Found {len(extracted_data)} records with schema: {schema['name']}")
                            
                            # Filter out empty or header rows
                            valid_contractors = []
                            for record in extracted_data:
                                # Check if record has meaningful data
                                if any(value and str(value).strip() and len(str(value).strip()) > 2 
                                      for value in record.values()):
                                    # Skip obvious header rows
                                    text_values = [str(v).lower() for v in record.values() if v]
                                    if not any(header in ' '.join(text_values) 
                                             for header in ['contractor', 'name', 'status', 'grading', 'crs']):
                                        valid_contractors.append(record)
                            
                            if valid_contractors:
                                contractors.extend(valid_contractors)
                                logger.info(f"✅ Added {len(valid_contractors)} valid contractor records")
                                break
                            
                except Exception as e:
                    logger.warning(f"Schema {schema['name']} failed: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Data extraction failed: {str(e)}")
            self.stats.errors.append(f"Extraction error: {str(e)}")
        
        return contractors
    
    async def save_contractors_to_supabase(self, contractors: List[Dict]):
        """Save contractors to Supabase with enhanced data mapping"""
        if not contractors:
            return
        
        try:
            processed_contractors = []
            
            for i, contractor in enumerate(contractors):
                # Map the data to our schema
                processed_contractor = {
                    'crs_number': (contractor.get('crs_number') or 
                                 contractor.get('col_1') or 
                                 contractor.get('data_cell_1') or 
                                 f'ALT_EXTRACTED_{i+1}'),
                    'contractor_name': (contractor.get('contractor_name') or 
                                      contractor.get('col_2') or 
                                      contractor.get('data_cell_2') or 
                                      'Unknown'),
                    'status': (contractor.get('status') or 
                             contractor.get('col_3') or 
                             contractor.get('data_cell_3')),
                    'grading': contractor.get('grading') or contractor.get('col_4'),
                    'province': contractor.get('province') or contractor.get('col_5'),
                    'city': contractor.get('city') or contractor.get('col_6'),
                    'contractor_status': 'alternative_extracted',
                    'scraped_at': datetime.utcnow().isoformat() + "Z"
                }
                
                # Only add if we have meaningful data
                if any(v and str(v).strip() and len(str(v).strip()) > 1 
                      for v in processed_contractor.values() if v):
                    processed_contractors.append(processed_contractor)
            
            if processed_contractors:
                # Batch insert
                batch_size = 100
                for i in range(0, len(processed_contractors), batch_size):
                    batch = processed_contractors[i:i+batch_size]
                    
                    result = supabase.table('contractors').upsert(
                        batch,
                        on_conflict='crs_number,contractor_status'
                    ).execute()
                    
                    if not hasattr(result, 'error') or not result.error:
                        logger.info(f"✅ Saved {len(batch)} contractors to database")
                        self.stats.new_contractors += len(batch)
                        self.stats.total_contractors += len(batch)
                    else:
                        logger.error(f"❌ Database save error: {result.error}")
                        
        except Exception as e:
            logger.error(f"Error saving contractors: {str(e)}")
            self.stats.errors.append(f"Save error: {str(e)}")
    
    async def run_alternative_scrape(self):
        """Run alternative scraping using the public contractor search endpoint"""
        start_time = datetime.utcnow()
        
        try:
            # Initialize scraping run
            await self.initialize_scraping_run('both')
            
            logger.info("🚀 Starting alternative CIDB contractor scraping...")
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # Explore the alternative endpoint
                page_info = await self.explore_alternative_endpoint(crawler)
                
                # Try search functionality
                contractors = await self.try_search_functionality(crawler)
                
                # Try direct extraction
                if not contractors:
                    contractors = await self.extract_contractor_data_alternative(crawler)
                
                # Save any data we found
                if contractors:
                    await self.save_contractors_to_supabase(contractors)
                    logger.info(f"🎉 Successfully extracted {len(contractors)} contractor records")
                else:
                    logger.warning("❌ No contractor data could be extracted from alternative endpoint")
            
            # Update final statistics
            end_time = datetime.utcnow()
            
            if self.stats.errors:
                status = 'partial' if contractors else 'failed'
            else:
                status = 'completed'
            
            await self.update_scraping_run(status, end_time)
            
            # Log final results
            duration = (end_time - start_time).total_seconds()
            logger.info(f"""
🎯 Alternative scraping completed in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Status: {status}
Total Contractors: {self.stats.total_contractors}
Errors: {len(self.stats.errors)}
            """)
            
            return {
                'run_id': self.stats.run_id,
                'status': status,
                'total_contractors': self.stats.total_contractors,
                'duration_seconds': duration,
                'errors': self.stats.errors,
                'page_info': page_info
            }
            
        except Exception as e:
            logger.error(f"Critical error during alternative scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise

async def main():
    """Main entry point for alternative scraper"""
    scraper = AlternativeCIDBScraper()
    
    try:
        result = await scraper.run_alternative_scrape()
        print(f"🎉 Alternative scraping completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Alternative scraping failed: {str(e)}")
        print(f"❌ Alternative scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
