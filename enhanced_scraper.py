#!/usr/bin/env python3
"""
Enhanced CIDB Contractor <PERSON><PERSON><PERSON>
Handles authentication and permission issues
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
CIDB_BASE_URL = "https://portal.cidb.org.za/RegisterOfContractors/"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingStats:
    """Track scraping statistics"""
    run_id: str
    total_pages: int = 0
    total_contractors: int = 0
    active_contractors: int = 0
    inactive_contractors: int = 0
    new_contractors: int = 0
    updated_contractors: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class EnhancedCIDBScraper:
    """Enhanced CIDB Contractor Scraper with authentication handling"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.browser_config = BrowserConfig(
            headless=False,  # Set to False to see what's happening
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )
    
    async def initialize_scraping_run(self, contractor_type: str) -> str:
        """Initialize a new scraping run in the database"""
        try:
            result = supabase.table('scraping_runs').insert({
                'run_id': self.stats.run_id,
                'status': 'started',
                'contractor_type': contractor_type,
                'scraper_version': '2.0.0',
                'user_agent': self.browser_config.user_agent,
                'notes': 'Enhanced scraper with authentication handling'
            }).execute()
            
            logger.info(f"Initialized scraping run: {self.stats.run_id}")
            return self.stats.run_id
            
        except Exception as e:
            logger.error(f"Failed to initialize scraping run: {str(e)}")
            raise
    
    async def update_scraping_run(self, status: str, end_time: Optional[datetime] = None):
        """Update scraping run status and statistics"""
        try:
            update_data = {
                'status': status,
                'total_pages_scraped': self.stats.total_pages,
                'total_contractors_found': self.stats.total_contractors,
                'active_contractors_count': self.stats.active_contractors,
                'inactive_contractors_count': self.stats.inactive_contractors,
                'new_contractors_added': self.stats.new_contractors,
                'updated_contractors_count': self.stats.updated_contractors,
                'errors_encountered': self.stats.errors
            }
            
            if end_time:
                update_data['end_time'] = end_time.isoformat()
            
            supabase.table('scraping_runs').update(update_data).eq('run_id', self.stats.run_id).execute()
            logger.info(f"Updated scraping run {self.stats.run_id} with status: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update scraping run: {str(e)}")
    
    async def explore_page_structure(self, crawler: AsyncWebCrawler) -> Dict:
        """Explore the page structure to understand what's available"""
        try:
            logger.info("Exploring CIDB page structure...")
            
            # First, let's try to load the page and see what we get
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,  # 30 seconds
                wait_for="css:body"  # Just wait for body to load
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            
            if result.success:
                logger.info("✅ Successfully loaded CIDB page")
                
                # Save the current page content for analysis
                with open("current_page.html", "w", encoding="utf-8") as f:
                    f.write(result.html)
                
                with open("current_page.md", "w", encoding="utf-8") as f:
                    f.write(result.markdown)
                
                # Look for any contractor-related content
                html_lower = result.html.lower()
                markdown_lower = result.markdown.lower()
                
                # Check for various elements
                elements_found = {
                    'has_table': 'table' in html_lower,
                    'has_contractors': 'contractor' in markdown_lower,
                    'has_form': 'form' in html_lower,
                    'has_search': 'search' in markdown_lower,
                    'has_results': 'results' in markdown_lower,
                    'has_records': 'records' in markdown_lower,
                    'has_permissions_error': 'permissions' in markdown_lower,
                    'has_login': 'login' in html_lower or 'sign in' in html_lower,
                    'page_length': len(result.html),
                    'markdown_length': len(result.markdown)
                }
                
                logger.info(f"Page analysis: {elements_found}")
                
                # Try to find any visible contractor data
                if 'no records to display' in markdown_lower:
                    logger.warning("Page shows 'no records to display'")
                    self.stats.errors.append("No records to display - may require authentication")
                
                if 'permissions' in markdown_lower:
                    logger.warning("Page shows permissions error")
                    self.stats.errors.append("Permissions error - authentication required")
                
                return elements_found
                
            else:
                logger.error(f"Failed to load page: {result.error_message}")
                self.stats.errors.append(f"Page load failed: {result.error_message}")
                return {}
                
        except Exception as e:
            logger.error(f"Error exploring page structure: {str(e)}")
            self.stats.errors.append(f"Page exploration error: {str(e)}")
            return {}
    
    async def try_alternative_extraction(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try alternative methods to extract any available data"""
        contractors = []
        
        try:
            logger.info("Trying alternative extraction methods...")
            
            # Method 1: Try to extract any table data that might be visible
            table_schema = {
                "name": "Any Table Data",
                "baseSelector": "table tr",
                "fields": [
                    {"name": "cell_1", "selector": "td:nth-child(1)", "type": "text"},
                    {"name": "cell_2", "selector": "td:nth-child(2)", "type": "text"},
                    {"name": "cell_3", "selector": "td:nth-child(3)", "type": "text"},
                    {"name": "cell_4", "selector": "td:nth-child(4)", "type": "text"},
                    {"name": "cell_5", "selector": "td:nth-child(5)", "type": "text"},
                ]
            }
            
            extraction_strategy = JsonCssExtractionStrategy(table_schema, verbose=True)
            config = CrawlerRunConfig(
                extraction_strategy=extraction_strategy,
                cache_mode=CacheMode.BYPASS,
                page_timeout=15000,
                wait_for="css:body"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            
            if result.success and result.extracted_content:
                extracted_data = json.loads(result.extracted_content)
                logger.info(f"Extracted {len(extracted_data)} table rows")
                
                # Filter out empty or header rows
                for row in extracted_data:
                    if any(cell and cell.strip() and not cell.strip().lower().startswith(('crs', 'contractor', 'status')) 
                           for cell in row.values()):
                        contractors.append(row)
            
            # Method 2: Try to extract any list items
            if not contractors:
                list_schema = {
                    "name": "List Items",
                    "baseSelector": "li",
                    "fields": [
                        {"name": "text", "selector": "", "type": "text"}
                    ]
                }
                
                extraction_strategy = JsonCssExtractionStrategy(list_schema, verbose=True)
                config = CrawlerRunConfig(
                    extraction_strategy=extraction_strategy,
                    cache_mode=CacheMode.BYPASS,
                    page_timeout=15000,
                    wait_for="css:body"
                )
                
                result = await crawler.arun(url=CIDB_BASE_URL, config=config)
                
                if result.success and result.extracted_content:
                    extracted_data = json.loads(result.extracted_content)
                    logger.info(f"Extracted {len(extracted_data)} list items")
                    
                    # Look for contractor-like data in list items
                    for item in extracted_data:
                        text = item.get('text', '').strip()
                        if text and len(text) > 10 and any(keyword in text.lower() 
                                                          for keyword in ['contractor', 'company', 'pty', 'ltd']):
                            contractors.append({'contractor_info': text})
            
            logger.info(f"Alternative extraction found {len(contractors)} potential records")
            return contractors
            
        except Exception as e:
            logger.error(f"Alternative extraction failed: {str(e)}")
            self.stats.errors.append(f"Alternative extraction error: {str(e)}")
            return []
    
    async def run_enhanced_scrape(self):
        """Run enhanced scraping with better error handling"""
        start_time = datetime.utcnow()
        
        try:
            # Initialize scraping run
            await self.initialize_scraping_run('both')
            
            logger.info("Starting enhanced CIDB contractor scraping...")
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # First, explore the page structure
                page_info = await self.explore_page_structure(crawler)
                
                # Try alternative extraction methods
                contractors = await self.try_alternative_extraction(crawler)
                
                if contractors:
                    logger.info(f"Found {len(contractors)} potential contractor records")
                    self.stats.total_contractors = len(contractors)
                    
                    # Save to database if we found anything useful
                    await self.save_alternative_data(contractors)
                else:
                    logger.info("No contractor data could be extracted")
            
            # Update final statistics
            end_time = datetime.utcnow()
            
            if self.stats.errors:
                status = 'partial' if contractors else 'failed'
            else:
                status = 'completed'
            
            await self.update_scraping_run(status, end_time)
            
            # Log final results
            duration = (end_time - start_time).total_seconds()
            logger.info(f"""
Enhanced scraping completed in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Status: {status}
Total Contractors: {self.stats.total_contractors}
Errors: {len(self.stats.errors)}
            """)
            
            if self.stats.errors:
                logger.warning(f"Errors encountered: {self.stats.errors}")
            
            return {
                'run_id': self.stats.run_id,
                'status': status,
                'total_contractors': self.stats.total_contractors,
                'duration_seconds': duration,
                'errors': self.stats.errors,
                'page_info': page_info
            }
            
        except Exception as e:
            logger.error(f"Critical error during enhanced scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise
    
    async def save_alternative_data(self, data: List[Dict]):
        """Save alternative extracted data"""
        if not data:
            return
        
        try:
            # Create a simple record for any data we found
            for i, record in enumerate(data):
                contractor_record = {
                    'crs_number': f'UNKNOWN_{i+1}',
                    'contractor_name': str(record),
                    'contractor_status': 'unknown',
                    'scraped_at': datetime.utcnow().isoformat() + "Z"
                }
                
                # Try to insert
                result = supabase.table('contractors').insert(contractor_record).execute()
                
                if not hasattr(result, 'error') or not result.error:
                    self.stats.new_contractors += 1
                    
        except Exception as e:
            logger.error(f"Error saving alternative data: {str(e)}")
            self.stats.errors.append(f"Data save error: {str(e)}")

async def main():
    """Main entry point for enhanced scraper"""
    scraper = EnhancedCIDBScraper()
    
    try:
        result = await scraper.run_enhanced_scrape()
        print(f"Enhanced scraping completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Enhanced scraping failed: {str(e)}")
        print(f"Enhanced scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
