#!/usr/bin/env python3
"""
Super Hybrid CIDB Scraper - Demonstration Script
Shows all capabilities and methods working together
"""

import asyncio
import json
from pathlib import Path
from super_hybrid_scraper import SuperHybridScraper

async def demo_basic_functionality():
    """Demonstrate basic scraper functionality"""
    print("🚀 SUPER HYBRID SCRAPER DEMONSTRATION")
    print("=" * 50)
    
    scraper = SuperHybridScraper()
    
    print(f"📊 Scraper initialized with run ID: {scraper.stats.run_id}")
    print(f"📁 Output directory: {scraper.output_dir}")
    
    # Test database connection
    print("\n🗄️ Testing database connection...")
    try:
        await scraper.initialize_scraping_run('demo')
        print("✅ Database connection successful")
        await scraper.update_scraping_run('completed')
        print("✅ Database operations working")
    except Exception as e:
        print(f"❌ Database error: {e}")
    
    return scraper

async def demo_crawl4ai_scraping(scraper):
    """Demonstrate Crawl4AI scraping capabilities"""
    print("\n🕷️ CRAWL4AI SCRAPING DEMONSTRATION")
    print("-" * 40)
    
    # Test with a reliable URL
    test_urls = [
        "https://httpbin.org/html",
        "https://example.com"
    ]
    
    for url in test_urls:
        try:
            print(f"\n🔍 Scraping: {url}")
            result = await scraper.crawl4ai_scrape(url)
            
            if result.get('success'):
                print(f"✅ Success! Content length: {len(result.get('markdown', ''))}")
                print(f"📸 Screenshot saved: {result.get('screenshot') is not None}")
                
                # Show first 200 characters of content
                content = result.get('markdown', '')[:200]
                print(f"📄 Content preview: {content}...")
                
            else:
                print("❌ Scraping failed")
                
        except Exception as e:
            print(f"❌ Error scraping {url}: {e}")

async def demo_file_processing(scraper):
    """Demonstrate file processing capabilities"""
    print("\n📄 FILE PROCESSING DEMONSTRATION")
    print("-" * 40)
    
    # Create sample files for testing
    test_files = []
    
    # Create a sample text file with contractor data
    text_file = Path("demo_contractors.txt")
    text_content = """
CIDB Contractor Registry - Sample Data

1. ABC Construction (Pty) Ltd
   CRS Number: 12345/CE/2024
   Status: Active
   Grading: 9CE
   Contact: 011-123-4567
   Email: <EMAIL>
   Province: Gauteng
   City: Johannesburg

2. XYZ Engineering Solutions
   CRS Number: 67890/GB/2024
   Status: Active
   Grading: 7GB
   Contact: 021-987-6543
   Email: <EMAIL>
   Province: Western Cape
   City: Cape Town

3. DEF Builders CC
   CRS Number: 11111/CE/2024
   Status: Inactive
   Grading: 5CE
   Contact: 031-555-1234
   Email: <EMAIL>
   Province: KwaZulu-Natal
   City: Durban
"""
    
    with open(text_file, 'w') as f:
        f.write(text_content)
    test_files.append(text_file)
    
    # Process each test file
    for file_path in test_files:
        try:
            print(f"\n🔍 Processing file: {file_path}")
            result = await scraper.process_file(str(file_path))
            
            contractors = result.get('contractors', [])
            print(f"✅ Found {len(contractors)} contractors")
            
            # Show details of found contractors
            for i, contractor in enumerate(contractors[:3]):  # Show first 3
                print(f"   {i+1}. {contractor.get('contractor_name', 'Unknown')}")
                print(f"      CRS: {contractor.get('crs_number', 'Unknown')}")
                print(f"      Contact: {contractor.get('contact_number', 'Unknown')}")
                
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
    
    # Clean up test files
    for file_path in test_files:
        if file_path.exists():
            file_path.unlink()

async def demo_rate_limiting(scraper):
    """Demonstrate rate limiting functionality"""
    print("\n⏱️ RATE LIMITING DEMONSTRATION")
    print("-" * 40)
    
    print("🔄 Testing rate limiter with multiple requests...")
    
    import time
    start_time = time.time()
    
    # Make several requests to test rate limiting
    for i in range(3):
        print(f"   Request {i+1}...")
        await scraper.rate_limiter.wait_if_needed()
    
    elapsed = time.time() - start_time
    print(f"✅ Rate limiting working (total time: {elapsed:.2f}s)")
    print(f"📊 Rate limit hits: {scraper.stats.rate_limit_hits}")

async def demo_hybrid_scraping(scraper):
    """Demonstrate hybrid scraping with multiple methods"""
    print("\n🔄 HYBRID SCRAPING DEMONSTRATION")
    print("-" * 40)
    
    # Test with a simple URL that should work
    test_url = "https://httpbin.org/html"
    
    try:
        print(f"🔍 Hybrid scraping: {test_url}")
        result = await scraper.hybrid_scrape_url(test_url)
        
        print(f"✅ Hybrid scraping completed!")
        print(f"📊 Total contractors found: {result.get('total_contractors', 0)}")
        print(f"🛠️ Methods used: {', '.join(result.get('methods_used', []))}")
        
        return result
        
    except Exception as e:
        print(f"❌ Hybrid scraping failed: {e}")
        return None

async def demo_statistics_tracking(scraper):
    """Demonstrate statistics tracking"""
    print("\n📊 STATISTICS TRACKING DEMONSTRATION")
    print("-" * 40)
    
    stats = scraper.stats
    
    print(f"🆔 Run ID: {stats.run_id}")
    print(f"📄 Total pages scraped: {stats.total_pages}")
    print(f"👥 Total contractors found: {stats.total_contractors}")
    print(f"🔥 FireCrawl requests: {stats.firecrawl_requests}")
    print(f"🕷️ Crawl4AI requests: {stats.crawl4ai_requests}")
    print(f"🤖 LLM requests: {stats.llm_requests}")
    print(f"📸 Screenshot analyses: {stats.screenshot_analyses}")
    print(f"📄 PDFs processed: {stats.pdf_processed}")
    print(f"📝 DOCX processed: {stats.docx_processed}")
    print(f"⏱️ Rate limit hits: {stats.rate_limit_hits}")
    print(f"❌ Errors encountered: {len(stats.errors)}")
    
    if stats.errors:
        print("\n🚨 Error details:")
        for i, error in enumerate(stats.errors[:3]):  # Show first 3 errors
            print(f"   {i+1}. {error}")

async def demo_output_files(scraper):
    """Demonstrate output file management"""
    print("\n📁 OUTPUT FILES DEMONSTRATION")
    print("-" * 40)
    
    # Check screenshots directory
    screenshots = list(scraper.screenshots_dir.glob("*.png"))
    print(f"📸 Screenshots captured: {len(screenshots)}")
    
    for screenshot in screenshots[-3:]:  # Show last 3
        print(f"   📷 {screenshot.name}")
    
    # Check documents directory
    documents = list(scraper.documents_dir.glob("*"))
    print(f"📄 Documents processed: {len(documents)}")
    
    # Check log files
    log_files = list(Path(".").glob("*.log"))
    print(f"📋 Log files: {len(log_files)}")
    
    for log_file in log_files:
        print(f"   📝 {log_file.name}")

async def demo_configuration_options():
    """Demonstrate configuration options"""
    print("\n⚙️ CONFIGURATION OPTIONS DEMONSTRATION")
    print("-" * 40)
    
    from dotenv import load_dotenv
    import os
    
    load_dotenv()
    
    # Show current configuration
    config_vars = [
        'SUPABASE_URL',
        'FIRECRAWL_API_KEY',
        'OPENAI_API_KEY',
        'ANTHROPIC_API_KEY',
        'FIRECRAWL_FREE_LIMIT',
        'RATE_LIMIT_DELAY'
    ]
    
    print("📋 Current configuration:")
    for var in config_vars:
        value = os.getenv(var, 'Not set')
        # Hide sensitive keys
        if 'KEY' in var and value != 'Not set':
            value = f"{value[:10]}...{value[-4:]}" if len(value) > 14 else "***"
        print(f"   {var}: {value}")

async def main():
    """Main demonstration function"""
    try:
        # Initialize scraper
        scraper = await demo_basic_functionality()
        
        # Demonstrate each capability
        await demo_crawl4ai_scraping(scraper)
        await demo_file_processing(scraper)
        await demo_rate_limiting(scraper)
        await demo_hybrid_scraping(scraper)
        await demo_statistics_tracking(scraper)
        await demo_output_files(scraper)
        await demo_configuration_options()
        
        print("\n🎉 DEMONSTRATION COMPLETED!")
        print("=" * 50)
        print("\n📚 Next steps:")
        print("1. Add API keys to .env file for full functionality")
        print("2. Run: python3 super_hybrid_scraper.py")
        print("3. Check results in Supabase dashboard")
        print("4. Review SUPER_HYBRID_GUIDE.md for advanced usage")
        
        print("\n🔑 To unlock full capabilities, add these API keys:")
        print("   • FireCrawl API: https://firecrawl.dev/ (500 free/month)")
        print("   • OpenAI API: https://platform.openai.com/")
        print("   • Anthropic API: https://console.anthropic.com/")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Demonstration interrupted by user")
        return False
    except Exception as e:
        print(f"\n\n❌ Demonstration failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n✨ Super Hybrid Scraper is ready for action! ✨")
    else:
        print("\n⚠️ Please check the setup and try again.")
