#!/usr/bin/env python3
"""
CIDB Interactive Scraper
Attempts to interact with the CIDB portal to access contractor data
Tries various methods to bypass authentication barriers
"""

import asyncio
import json
import time
from datetime import datetime
from typing import List, Dict
import logging
from pathlib import Path

from super_hybrid_scraper import <PERSON>HybridScraper
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cidb_interactive_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CIDBInteractiveScraper(SuperHybridScraper):
    """Interactive scraper that tries to access CIDB data through various methods"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://portal.cidb.org.za/RegisterOfContractors/"
        self.alternative_urls = [
            "https://registers.cidb.org.za/",
            "https://registers.cidb.org.za/PublicContractors/ContractorSearch",
            "https://www.cidb.org.za/",
            "https://portal.cidb.org.za/RegisterOfContractors/?preview=true",
            "https://portal.cidb.org.za/RegisterOfContractors/?view=public"
        ]
    
    async def try_preview_mode(self, crawler) -> Dict:
        """Try to access preview mode as suggested by the page"""
        logger.info("🔍 Trying preview mode...")
        
        try:
            # Strategy 1: Click preview button if it exists
            preview_actions = [
                {"type": "wait", "milliseconds": 3000},
                {"type": "click", "selector": "button:contains('preview'), .preview, [data-action='preview']"},
                {"type": "wait", "milliseconds": 5000},
            ]
            
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                wait_for="css:body",
                screenshot=True
            )
            
            result = await crawler.arun(url=self.base_url, config=config, actions=preview_actions)
            
            if result.success:
                logger.info("✅ Preview mode attempt completed")
                
                # Check if we got different content
                if "You don't have permissions" not in result.markdown:
                    logger.info("🎉 Preview mode may have worked!")
                    return await self.extract_contractor_data_from_result(result, "preview_mode")
                else:
                    logger.warning("❌ Preview mode still shows permission error")
            
            return {"contractors": [], "method": "preview_mode", "success": False}
            
        except Exception as e:
            logger.error(f"Preview mode failed: {e}")
            return {"contractors": [], "method": "preview_mode", "error": str(e)}
    
    async def try_alternative_urls(self, crawler) -> List[Dict]:
        """Try alternative CIDB URLs"""
        logger.info("🔍 Trying alternative URLs...")
        
        results = []
        
        for url in self.alternative_urls:
            try:
                logger.info(f"   Trying: {url}")
                
                config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    page_timeout=30000,
                    wait_for="css:body",
                    screenshot=True
                )
                
                result = await crawler.arun(url=url, config=config)
                
                if result.success:
                    # Check if this URL has different content
                    if "You don't have permissions" not in result.markdown:
                        logger.info(f"✅ {url} may have accessible content")
                        
                        # Save screenshot for this URL
                        if result.screenshot:
                            import base64
                            screenshot_path = self.screenshots_dir / f"alternative_{url.replace('/', '_').replace(':', '')}.png"
                            with open(screenshot_path, 'wb') as f:
                                f.write(base64.b64decode(result.screenshot))
                        
                        extraction_result = await self.extract_contractor_data_from_result(result, f"alternative_url_{url}")
                        results.append(extraction_result)
                    else:
                        logger.warning(f"❌ {url} also shows permission error")
                else:
                    logger.warning(f"❌ Failed to load {url}")
                
                # Rate limiting
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Error with {url}: {e}")
                continue
        
        return results
    
    async def try_form_interactions(self, crawler) -> Dict:
        """Try to interact with search forms and filters"""
        logger.info("🔍 Trying form interactions...")
        
        try:
            # Try to interact with search filters
            form_actions = [
                {"type": "wait", "milliseconds": 3000},
                
                # Try to select "Active" status
                {"type": "click", "selector": "select[name*='status'], select[name*='Status']"},
                {"type": "wait", "milliseconds": 1000},
                {"type": "click", "selector": "option[value='Active'], option:contains('Active')"},
                {"type": "wait", "milliseconds": 1000},
                
                # Try to select a province
                {"type": "click", "selector": "select[name*='province'], select[name*='Province']"},
                {"type": "wait", "milliseconds": 1000},
                {"type": "click", "selector": "option[value='Gauteng'], option:contains('Gauteng')"},
                {"type": "wait", "milliseconds": 1000},
                
                # Try to click search button
                {"type": "click", "selector": "button[type='submit'], .search-button, input[type='submit']"},
                {"type": "wait", "milliseconds": 5000},
            ]
            
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                wait_for="css:body",
                screenshot=True
            )
            
            result = await crawler.arun(url=self.base_url, config=config, actions=form_actions)
            
            if result.success:
                logger.info("✅ Form interaction completed")
                
                # Check if we got different content
                if "There are no records to display" not in result.markdown:
                    logger.info("🎉 Form interaction may have worked!")
                    return await self.extract_contractor_data_from_result(result, "form_interaction")
                else:
                    logger.warning("❌ Form interaction still shows no records")
            
            return {"contractors": [], "method": "form_interaction", "success": False}
            
        except Exception as e:
            logger.error(f"Form interaction failed: {e}")
            return {"contractors": [], "method": "form_interaction", "error": str(e)}
    
    async def try_direct_api_calls(self) -> Dict:
        """Try to find and call CIDB APIs directly"""
        logger.info("🔍 Trying direct API calls...")
        
        try:
            import requests
            
            # Common API endpoints to try
            api_endpoints = [
                "https://portal.cidb.org.za/api/contractors",
                "https://portal.cidb.org.za/api/RegisterOfContractors",
                "https://registers.cidb.org.za/api/contractors",
                "https://registers.cidb.org.za/api/PublicContractors",
                "https://portal.cidb.org.za/_api/web/lists/getbytitle('Contractors')/items",
                "https://portal.cidb.org.za/RegisterOfContractors/_api/web/lists"
            ]
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://portal.cidb.org.za/RegisterOfContractors/'
            }
            
            for endpoint in api_endpoints:
                try:
                    logger.info(f"   Trying API: {endpoint}")
                    
                    response = requests.get(endpoint, headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        logger.info(f"✅ API call successful: {endpoint}")
                        
                        try:
                            data = response.json()
                            if data and isinstance(data, (list, dict)):
                                logger.info(f"🎉 Got JSON data from {endpoint}")
                                return {
                                    "contractors": data if isinstance(data, list) else [data],
                                    "method": f"direct_api_{endpoint}",
                                    "success": True
                                }
                        except json.JSONDecodeError:
                            # Try to parse as text
                            text_data = response.text
                            if len(text_data) > 100 and "contractor" in text_data.lower():
                                logger.info(f"🎉 Got text data from {endpoint}")
                                return {
                                    "raw_data": text_data,
                                    "method": f"direct_api_text_{endpoint}",
                                    "success": True
                                }
                    else:
                        logger.warning(f"❌ API call failed: {endpoint} - Status {response.status_code}")
                
                except requests.RequestException as e:
                    logger.warning(f"❌ API call error: {endpoint} - {e}")
                    continue
                
                # Rate limiting
                await asyncio.sleep(1)
            
            return {"contractors": [], "method": "direct_api", "success": False}
            
        except Exception as e:
            logger.error(f"Direct API calls failed: {e}")
            return {"contractors": [], "method": "direct_api", "error": str(e)}
    
    async def extract_contractor_data_from_result(self, result, method_name: str) -> Dict:
        """Extract contractor data from a crawl result"""
        try:
            contractors = []
            
            # Save content for analysis
            content_file = self.output_dir / f"{method_name}_content.md"
            with open(content_file, 'w') as f:
                f.write(result.markdown)
            
            # Try CSS extraction
            if result.extracted_content:
                try:
                    extracted_data = json.loads(result.extracted_content)
                    if extracted_data:
                        contractors.extend(extracted_data)
                        logger.info(f"✅ CSS extraction found {len(extracted_data)} contractors")
                except json.JSONDecodeError:
                    pass
            
            # Try LLM analysis if available
            if result.markdown and len(result.markdown) > 1000:
                llm_result = await self.llm_extract_from_content(result.markdown, method_name)
                if llm_result.get('contractors'):
                    contractors.extend(llm_result['contractors'])
                    logger.info(f"✅ LLM extraction found {len(llm_result['contractors'])} contractors")
            
            return {
                "contractors": contractors,
                "method": method_name,
                "success": len(contractors) > 0,
                "content_length": len(result.markdown),
                "content_file": str(content_file)
            }
            
        except Exception as e:
            logger.error(f"Extraction failed for {method_name}: {e}")
            return {"contractors": [], "method": method_name, "error": str(e)}
    
    async def run_interactive_scraping(self) -> Dict:
        """Run comprehensive interactive scraping"""
        start_time = datetime.utcnow()
        
        try:
            # Initialize scraping run
            await self.initialize_scraping_run('cidb_interactive')
            
            logger.info("🚀 Starting CIDB interactive scraping...")
            
            all_results = []
            total_contractors = 0
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                
                # Method 1: Try preview mode
                preview_result = await self.try_preview_mode(crawler)
                all_results.append(preview_result)
                total_contractors += len(preview_result.get('contractors', []))
                
                # Method 2: Try form interactions
                form_result = await self.try_form_interactions(crawler)
                all_results.append(form_result)
                total_contractors += len(form_result.get('contractors', []))
                
                # Method 3: Try alternative URLs
                alt_results = await self.try_alternative_urls(crawler)
                all_results.extend(alt_results)
                for result in alt_results:
                    total_contractors += len(result.get('contractors', []))
            
            # Method 4: Try direct API calls
            api_result = await self.try_direct_api_calls()
            all_results.append(api_result)
            total_contractors += len(api_result.get('contractors', []))
            
            # Save all contractors found
            all_contractors = []
            for result in all_results:
                if result.get('contractors'):
                    all_contractors.extend(result['contractors'])
            
            if all_contractors:
                await self.save_contractors_to_supabase(all_contractors, "interactive_scraping")
            
            # Update final statistics
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            status = 'completed' if total_contractors > 0 else 'partial'
            await self.update_scraping_run(status, end_time)
            
            # Generate comprehensive report
            report = {
                'run_id': self.stats.run_id,
                'status': status,
                'duration_seconds': duration,
                'total_contractors': total_contractors,
                'methods_tried': len(all_results),
                'successful_methods': len([r for r in all_results if r.get('success')]),
                'results': all_results,
                'errors': self.stats.errors
            }
            
            # Save detailed report
            report_file = self.output_dir / f"cidb_interactive_report_{self.stats.run_id}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"\n🎉 CIDB INTERACTIVE SCRAPING COMPLETED!")
            logger.info("=" * 60)
            logger.info(f"📊 Final Results:")
            logger.info(f"   Total contractors: {total_contractors}")
            logger.info(f"   Methods tried: {len(all_results)}")
            logger.info(f"   Successful methods: {len([r for r in all_results if r.get('success')])}")
            logger.info(f"   Duration: {duration:.2f} seconds")
            logger.info(f"   Report saved: {report_file}")
            
            return report
            
        except Exception as e:
            logger.error(f"Critical error during interactive scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise

async def main():
    """Main entry point for interactive scraper"""
    scraper = CIDBInteractiveScraper()
    
    try:
        result = await scraper.run_interactive_scraping()
        
        print(f"\n🎉 CIDB Interactive Scraping Results:")
        print(f"👥 Total contractors: {result['total_contractors']}")
        print(f"🔧 Methods tried: {result['methods_tried']}")
        print(f"✅ Successful methods: {result['successful_methods']}")
        print(f"⏱️ Duration: {result['duration_seconds']:.2f} seconds")
        
        if result['total_contractors'] > 0:
            print(f"\n✅ SUCCESS! Found contractor data from CIDB portal")
            print(f"🗄️ Data saved to Supabase database")
        else:
            print(f"\n⚠️ No contractor data extracted. All methods encountered barriers.")
            print(f"💡 Recommendations:")
            print(f"   1. Contact CIDB for official API access")
            print(f"   2. Request data export from CIDB")
            print(f"   3. Use alternative data sources")
        
        return result
        
    except Exception as e:
        logger.error(f"Interactive scraping failed: {str(e)}")
        print(f"❌ Interactive scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
