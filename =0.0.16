Defaulting to user installation because normal site-packages is not writeable
Collecting firecrawl-py
  Downloading firecrawl_py-2.15.0-py3-none-any.whl.metadata (7.2 kB)
Requirement already satisfied: requests in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from firecrawl-py) (2.32.4)
Requirement already satisfied: python-dotenv in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from firecrawl-py) (1.1.1)
Requirement already satisfied: websockets in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from firecrawl-py) (15.0.1)
Collecting nest-asyncio (from firecrawl-py)
  Downloading nest_asyncio-1.6.0-py3-none-any.whl.metadata (2.8 kB)
Requirement already satisfied: pydantic in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from firecrawl-py) (2.11.7)
Requirement already satisfied: aiohttp in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from firecrawl-py) (3.12.13)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (1.4.0)
Requirement already satisfied: async-timeout<6.0,>=4.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (5.0.1)
Requirement already satisfied: attrs>=17.3.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (1.7.0)
Requirement already satisfied: multidict<7.0,>=4.5 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (6.6.3)
Requirement already satisfied: propcache>=0.2.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (0.3.2)
Requirement already satisfied: yarl<2.0,>=1.17.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from aiohttp->firecrawl-py) (1.20.1)
Requirement already satisfied: typing-extensions>=4.1.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from multidict<7.0,>=4.5->aiohttp->firecrawl-py) (4.14.1)
Requirement already satisfied: idna>=2.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from yarl<2.0,>=1.17.0->aiohttp->firecrawl-py) (3.10)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pydantic->firecrawl-py) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pydantic->firecrawl-py) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pydantic->firecrawl-py) (0.4.1)
Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->firecrawl-py) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->firecrawl-py) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->firecrawl-py) (2025.7.9)
Downloading firecrawl_py-2.15.0-py3-none-any.whl (75 kB)
Downloading nest_asyncio-1.6.0-py3-none-any.whl (5.2 kB)
Installing collected packages: nest-asyncio, firecrawl-py

Successfully installed firecrawl-py-2.15.0 nest-asyncio-1.6.0
