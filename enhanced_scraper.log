2025-07-10 15:06:59,655 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 15:06:59,659 - ERROR - Failed to initialize scraping run: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (4, 6938e734-7ef0-4662-8053-87a15775edc6, started, enhanced, 0, 0, 0, 0, 0, 0, null, 2025-07-10 13:06:59.625289+00, null, null, 2.0.0, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KH..., Enhanced scraper with authentication handling).'}
2025-07-10 15:06:59,659 - ERROR - Critical error during enhanced scraping: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (4, 6938e734-7ef0-4662-8053-87a15775edc6, started, enhanced, 0, 0, 0, 0, 0, 0, null, 2025-07-10 13:06:59.625289+00, null, null, 2.0.0, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KH..., Enhanced scraper with authentication handling).'}
2025-07-10 15:07:00,213 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.6938e734-7ef0-4662-8053-87a15775edc6 "HTTP/2 200 OK"
2025-07-10 15:07:00,219 - INFO - Updated scraping run 6938e734-7ef0-4662-8053-87a15775edc6 with status: failed
2025-07-10 15:07:00,220 - ERROR - Enhanced scraping failed: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (4, 6938e734-7ef0-4662-8053-87a15775edc6, started, enhanced, 0, 0, 0, 0, 0, 0, null, 2025-07-10 13:06:59.625289+00, null, null, 2.0.0, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KH..., Enhanced scraper with authentication handling).'}
2025-07-10 15:07:20,031 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 15:07:20,033 - INFO - Initialized scraping run: 8e2d3cc4-f521-46f8-a4db-b6f0a7ca122c
2025-07-10 15:07:20,033 - INFO - Starting enhanced CIDB contractor scraping...
2025-07-10 15:07:20,752 - INFO - Exploring CIDB page structure...
2025-07-10 15:07:23,850 - INFO - ✅ Successfully loaded CIDB page
2025-07-10 15:07:23,853 - INFO - Page analysis: {'has_table': True, 'has_contractors': True, 'has_form': True, 'has_search': True, 'has_results': True, 'has_records': True, 'has_permissions_error': True, 'has_login': True, 'page_length': 541677, 'markdown_length': 26295}
2025-07-10 15:07:23,853 - WARNING - Page shows 'no records to display'
2025-07-10 15:07:23,853 - WARNING - Page shows permissions error
2025-07-10 15:07:23,853 - INFO - Trying alternative extraction methods...
2025-07-10 15:07:27,021 - INFO - Extracted 0 table rows
2025-07-10 15:07:28,884 - INFO - Extracted 0 list items
2025-07-10 15:07:28,884 - INFO - Alternative extraction found 0 potential records
2025-07-10 15:07:28,884 - INFO - No contractor data could be extracted
2025-07-10 15:07:29,283 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.8e2d3cc4-f521-46f8-a4db-b6f0a7ca122c "HTTP/2 200 OK"
2025-07-10 15:07:29,289 - INFO - Updated scraping run 8e2d3cc4-f521-46f8-a4db-b6f0a7ca122c with status: failed
2025-07-10 15:07:29,290 - INFO - 
Enhanced scraping completed in 9.57 seconds
Run ID: 8e2d3cc4-f521-46f8-a4db-b6f0a7ca122c
Status: failed
Total Contractors: 0
Errors: 2
            
2025-07-10 15:07:29,290 - WARNING - Errors encountered: ['No records to display - may require authentication', 'Permissions error - authentication required']
2025-07-10 15:15:44,615 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 15:15:44,621 - INFO - Initialized scraping run: 026b5643-7946-40aa-931d-cc2bc3f8c23e
2025-07-10 15:15:44,621 - INFO - Starting enhanced CIDB contractor scraping...
2025-07-10 15:15:45,627 - INFO - Exploring CIDB page structure...
2025-07-10 15:15:48,718 - INFO - ✅ Successfully loaded CIDB page
2025-07-10 15:15:48,721 - INFO - Page analysis: {'has_table': True, 'has_contractors': True, 'has_form': True, 'has_search': True, 'has_results': True, 'has_records': True, 'has_permissions_error': True, 'has_login': True, 'page_length': 541677, 'markdown_length': 26295}
2025-07-10 15:15:48,721 - WARNING - Page shows 'no records to display'
2025-07-10 15:15:48,721 - WARNING - Page shows permissions error
2025-07-10 15:15:48,721 - INFO - Trying alternative extraction methods...
2025-07-10 15:15:51,762 - INFO - Extracted 0 table rows
2025-07-10 15:15:53,489 - INFO - Extracted 0 list items
2025-07-10 15:15:53,489 - INFO - Alternative extraction found 0 potential records
2025-07-10 15:15:53,489 - INFO - No contractor data could be extracted
2025-07-10 15:15:53,838 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.026b5643-7946-40aa-931d-cc2bc3f8c23e "HTTP/2 200 OK"
2025-07-10 15:15:53,839 - INFO - Updated scraping run 026b5643-7946-40aa-931d-cc2bc3f8c23e with status: failed
2025-07-10 15:15:53,840 - INFO - 
Enhanced scraping completed in 9.94 seconds
Run ID: 026b5643-7946-40aa-931d-cc2bc3f8c23e
Status: failed
Total Contractors: 0
Errors: 2
            
2025-07-10 15:15:53,840 - WARNING - Errors encountered: ['No records to display - may require authentication', 'Permissions error - authentication required']
