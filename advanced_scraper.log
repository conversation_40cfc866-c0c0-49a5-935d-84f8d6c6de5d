2025-07-10 15:19:48,004 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 15:19:48,011 - INFO - Initialized scraping run: 762ff47d-4486-46cf-abfb-8f29c5b6397a
2025-07-10 15:19:48,011 - INFO - 🚀 Starting advanced CIDB contractor scraping...
2025-07-10 15:19:48,512 - INFO - 🔍 Attempting search bypass strategy...
2025-07-10 15:19:51,619 - INFO - Search bypass result: {'success': True, 'results': [{'success': True, 'result': {}}]}
2025-07-10 15:19:56,177 - INFO - 🌐 Attempting direct API access...
2025-07-10 15:19:58,056 - INFO - API discovery result: {'success': True, 'results': [{'success': True, 'result': {}}]}
2025-07-10 15:19:58,056 - INFO - 🖼️ Checking for iframe content...
2025-07-10 15:20:00,075 - INFO - Iframe analysis: {'success': True, 'results': [{'success': True, 'result': {}}]}
2025-07-10 15:20:04,775 - WARNING - ❌ No contractor data could be extracted with any strategy
2025-07-10 15:20:05,424 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.762ff47d-4486-46cf-abfb-8f29c5b6397a "HTTP/2 200 OK"
2025-07-10 15:20:05,428 - INFO - Updated scraping run 762ff47d-4486-46cf-abfb-8f29c5b6397a with status: completed
2025-07-10 15:20:05,428 - INFO - 
🎯 Advanced scraping completed in 17.13 seconds
Run ID: 762ff47d-4486-46cf-abfb-8f29c5b6397a
Status: completed
Total Contractors: 0
Errors: 0
            
