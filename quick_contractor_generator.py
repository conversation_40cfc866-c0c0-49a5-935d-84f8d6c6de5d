#!/usr/bin/env python3
"""
Quick Contractor Generator
Generates 20 realistic CIDB contractors and saves to Supabase
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import openai
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Initialize clients
supabase: Client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

async def generate_realistic_contractors():
    """Generate 20 realistic South African CIDB contractors"""
    print("🏗️ GENERATING 20 REALISTIC CIDB CONTRACTORS")
    print("=" * 50)
    
    # Enhanced prompt for realistic South African contractors
    prompt = """
    Generate exactly 20 realistic South African CIDB contractors with authentic data.
    
    Use these guidelines:
    - Company names should sound like real SA construction companies
    - Use real South African provinces and cities
    - CRS numbers should follow format: XXXXX/XX/2024 (where XX is CE, GB, EB, etc.)
    - Grading should be 1-9 followed by class (CE, GB, EB, ME, etc.)
    - Phone numbers in SA format: ************, ************, etc.
    - Email addresses should match company names
    - Mix of Active/Inactive status
    - Realistic B-BBEE levels (1-8)
    - Mix of emerging and established contractors
    
    South African Provinces: Gauteng, Western Cape, KwaZulu-Natal, Eastern Cape, Free State, Limpopo, Mpumalanga, North West, Northern Cape
    
    Major Cities: Johannesburg, Cape Town, Durban, Pretoria, Port Elizabeth, Bloemfontein, Polokwane, Nelspruit, Rustenburg, Kimberley
    
    Return in this EXACT JSON format:
    {
        "contractors": [
            {
                "crs_number": "12345/CE/2024",
                "contractor_name": "ABC Construction (Pty) Ltd",
                "status": "Active",
                "grading": "7CE",
                "expiry_date": "2025-12-31",
                "trading_name": "ABC Builders",
                "province": "Gauteng",
                "city": "Johannesburg",
                "bbbee_status": "Level 4",
                "potentially_emerging": "Yes",
                "contact_number": "************",
                "email_address": "<EMAIL>"
            }
        ],
        "total_found": 20
    }
    
    Generate exactly 20 unique contractors with varied but realistic data.
    """
    
    try:
        print("🤖 Generating contractors with OpenAI...")
        
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are an expert on South African construction companies and CIDB registration. Generate realistic, varied contractor data."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"},
            temperature=0.8  # Higher temperature for variety
        )
        
        result = json.loads(response.choices[0].message.content)
        
        if result.get('contractors'):
            contractors = result['contractors']
            print(f"✅ Generated {len(contractors)} contractors")
            
            # Show sample data
            print(f"\n📋 SAMPLE CONTRACTORS:")
            for i, contractor in enumerate(contractors[:5]):
                print(f"   {i+1}. {contractor.get('contractor_name', 'Unknown')}")
                print(f"      CRS: {contractor.get('crs_number', 'Unknown')}")
                print(f"      Status: {contractor.get('status', 'Unknown')}")
                print(f"      Province: {contractor.get('province', 'Unknown')}")
                print(f"      Contact: {contractor.get('contact_number', 'Unknown')}")
                print(f"      Email: {contractor.get('email_address', 'Unknown')}")
                print()
            
            return contractors
        else:
            print("❌ No contractors generated")
            return []
            
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        return []

async def save_contractors_to_supabase(contractors):
    """Save contractors to Supabase database"""
    print(f"\n🗄️ SAVING {len(contractors)} CONTRACTORS TO SUPABASE")
    print("-" * 50)
    
    try:
        # Prepare contractors for database
        processed_contractors = []
        
        for i, contractor in enumerate(contractors):
            processed_contractor = {
                'crs_number': contractor.get('crs_number', f'GEN_{i+1:05d}/CE/2024'),
                'contractor_name': contractor.get('contractor_name', f'Generated Contractor {i+1}'),
                'status': contractor.get('status', 'Active'),
                'grading': contractor.get('grading', '5CE'),
                'expiry_date': contractor.get('expiry_date', '2025-12-31'),
                'trading_name': contractor.get('trading_name'),
                'province': contractor.get('province', 'Gauteng'),
                'city': contractor.get('city', 'Johannesburg'),
                'bbbee_status': contractor.get('bbbee_status', 'Level 4'),
                'potentially_emerging': contractor.get('potentially_emerging', 'No'),
                'contact_number': contractor.get('contact_number', '011-000-0000'),
                'email_address': contractor.get('email_address', '<EMAIL>'),
                'contractor_status': 'generated_sample',
                'scraped_at': datetime.utcnow().isoformat() + "Z"
            }
            processed_contractors.append(processed_contractor)
        
        # Batch insert to Supabase
        batch_size = 10
        total_saved = 0
        
        for i in range(0, len(processed_contractors), batch_size):
            batch = processed_contractors[i:i+batch_size]
            
            print(f"   💾 Saving batch {i//batch_size + 1} ({len(batch)} contractors)...")
            
            result = supabase.table('contractors').upsert(
                batch,
                on_conflict='crs_number,contractor_status'
            ).execute()
            
            if not hasattr(result, 'error') or not result.error:
                total_saved += len(batch)
                print(f"   ✅ Batch {i//batch_size + 1} saved successfully")
            else:
                print(f"   ❌ Batch {i//batch_size + 1} failed: {result.error}")
        
        print(f"\n✅ Successfully saved {total_saved}/{len(processed_contractors)} contractors to Supabase!")
        
        return total_saved
        
    except Exception as e:
        print(f"❌ Failed to save to Supabase: {e}")
        return 0

async def verify_data_in_supabase():
    """Verify the data was saved correctly"""
    print(f"\n🔍 VERIFYING DATA IN SUPABASE")
    print("-" * 50)
    
    try:
        # Query the contractors table
        result = supabase.table('contractors').select('*').eq('contractor_status', 'generated_sample').execute()
        
        if result.data:
            contractors = result.data
            print(f"✅ Found {len(contractors)} contractors in database")
            
            print(f"\n📊 VERIFICATION SAMPLE:")
            for i, contractor in enumerate(contractors[:3]):
                print(f"   {i+1}. {contractor.get('contractor_name', 'Unknown')}")
                print(f"      CRS: {contractor.get('crs_number', 'Unknown')}")
                print(f"      Status: {contractor.get('status', 'Unknown')}")
                print(f"      Province: {contractor.get('province', 'Unknown')}")
                print(f"      Contact: {contractor.get('contact_number', 'Unknown')}")
                print(f"      Email: {contractor.get('email_address', 'Unknown')}")
                print(f"      Saved at: {contractor.get('scraped_at', 'Unknown')}")
                print()
            
            return len(contractors)
        else:
            print("❌ No contractors found in database")
            return 0
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return 0

async def main():
    """Main execution function"""
    print("🚀 QUICK CONTRACTOR GENERATION & SUPABASE STORAGE")
    print("=" * 60)
    
    try:
        # Step 1: Generate contractors
        contractors = await generate_realistic_contractors()
        
        if not contractors:
            print("❌ No contractors generated. Exiting.")
            return
        
        # Step 2: Save to Supabase
        saved_count = await save_contractors_to_supabase(contractors)
        
        # Step 3: Verify data
        verified_count = await verify_data_in_supabase()
        
        # Final summary
        print(f"\n🎯 FINAL SUMMARY")
        print("=" * 30)
        print(f"📊 Contractors generated: {len(contractors)}")
        print(f"💾 Contractors saved: {saved_count}")
        print(f"✅ Contractors verified: {verified_count}")
        
        if verified_count > 0:
            print(f"\n🎉 SUCCESS! {verified_count} contractors are now in your Supabase database!")
            print(f"🗄️ Table: contractors")
            print(f"🏷️ Status: generated_sample")
            print(f"🔍 Query: SELECT * FROM contractors WHERE contractor_status = 'generated_sample';")
            
            print(f"\n📋 NEXT STEPS:")
            print(f"   1. Check your Supabase dashboard")
            print(f"   2. Query the contractors table")
            print(f"   3. Use this data for testing and development")
            print(f"   4. Replace with real CIDB data when access is obtained")
        else:
            print(f"\n⚠️ No contractors verified in database")
            print(f"💡 Check Supabase connection and permissions")
        
        return {
            'generated': len(contractors),
            'saved': saved_count,
            'verified': verified_count,
            'success': verified_count > 0
        }
        
    except Exception as e:
        print(f"❌ Process failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
