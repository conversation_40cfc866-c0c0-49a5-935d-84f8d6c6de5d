#!/usr/bin/env python3
"""
Super Hybrid CIDB Contractor Scraper
Combines FireCrawl API, Crawl4AI, LLM extraction, and screenshot analysis
Supports PDF, DOCX, and other file formats with advanced rate limiting
"""

import asyncio
import json
import os
import uuid
import time
import base64
from datetime import datetime
from typing import List, Dict, Optional, Any, Union
import logging
from dataclasses import dataclass, field
from pathlib import Path
import mimetypes

# Core libraries
from firecrawl import FirecrawlApp
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv
from pydantic import BaseModel, Field

# LLM libraries
import openai
from anthropic import Anthropic

# Document processing
import PyPDF2
import docx
from PIL import Image
import pytesseract

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
FIRECRAWL_API_KEY = os.getenv('FIRECRAWL_API_KEY')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')

# Rate limiting configuration
FIRECRAWL_FREE_LIMIT = 500  # Free tier limit
RATE_LIMIT_DELAY = 2  # Seconds between requests

# Initialize clients
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
firecrawl_app = FirecrawlApp(api_key=FIRECRAWL_API_KEY) if FIRECRAWL_API_KEY else None
openai_client = openai.OpenAI(api_key=OPENAI_API_KEY) if OPENAI_API_KEY else None
anthropic_client = Anthropic(api_key=ANTHROPIC_API_KEY) if ANTHROPIC_API_KEY else None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('super_hybrid_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Pydantic schemas for structured extraction
class ContractorData(BaseModel):
    """Schema for CIDB contractor data"""
    crs_number: Optional[str] = Field(description="Contractor registration number")
    contractor_name: Optional[str] = Field(description="Company name")
    status: Optional[str] = Field(description="Registration status (Active/Inactive)")
    grading: Optional[str] = Field(description="CIDB grading level")
    expiry_date: Optional[str] = Field(description="Registration expiry date")
    trading_name: Optional[str] = Field(description="Trading name")
    province: Optional[str] = Field(description="Province location")
    city: Optional[str] = Field(description="City location")
    bbbee_status: Optional[str] = Field(description="B-BBEE status")
    potentially_emerging: Optional[str] = Field(description="Emerging contractor flag")
    contact_number: Optional[str] = Field(description="Contact phone number")
    email_address: Optional[str] = Field(description="Email address")

class ContractorList(BaseModel):
    """Schema for list of contractors"""
    contractors: List[ContractorData] = Field(description="List of CIDB contractors")
    total_found: int = Field(description="Total number of contractors found")
    page_number: Optional[int] = Field(description="Page number if applicable")

@dataclass
class ScrapingStats:
    """Enhanced scraping statistics"""
    run_id: str
    total_pages: int = 0
    total_contractors: int = 0
    firecrawl_requests: int = 0
    crawl4ai_requests: int = 0
    llm_requests: int = 0
    screenshot_analyses: int = 0
    pdf_processed: int = 0
    docx_processed: int = 0
    errors: List[str] = field(default_factory=list)
    rate_limit_hits: int = 0
    
class RateLimiter:
    """Rate limiter for API calls"""
    def __init__(self, max_requests: int = FIRECRAWL_FREE_LIMIT, time_window: int = 3600):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    async def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        now = time.time()
        # Remove old requests outside time window
        self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]
        
        if len(self.requests) >= self.max_requests:
            sleep_time = self.time_window - (now - self.requests[0]) + 1
            logger.warning(f"Rate limit reached. Sleeping for {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)
            return True
        
        self.requests.append(now)
        await asyncio.sleep(RATE_LIMIT_DELAY)  # Basic delay between requests
        return False

class SuperHybridScraper:
    """Super hybrid scraper combining multiple advanced techniques"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.rate_limiter = RateLimiter()
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )
        
        # Create output directories
        self.output_dir = Path("scraper_output")
        self.screenshots_dir = self.output_dir / "screenshots"
        self.documents_dir = self.output_dir / "documents"
        
        for dir_path in [self.output_dir, self.screenshots_dir, self.documents_dir]:
            dir_path.mkdir(exist_ok=True)
    
    async def initialize_scraping_run(self, contractor_type: str = "hybrid") -> str:
        """Initialize a new scraping run in the database"""
        try:
            result = supabase.table('scraping_runs').insert({
                'run_id': self.stats.run_id,
                'status': 'started',
                'contractor_type': contractor_type,
                'scraper_version': '5.0.0-hybrid',
                'user_agent': self.browser_config.user_agent,
                'notes': 'Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screenshot analysis'
            }).execute()
            
            logger.info(f"🚀 Initialized hybrid scraping run: {self.stats.run_id}")
            return self.stats.run_id
            
        except Exception as e:
            logger.error(f"Failed to initialize scraping run: {str(e)}")
            raise
    
    async def update_scraping_run(self, status: str, end_time: Optional[datetime] = None):
        """Update scraping run with enhanced statistics"""
        try:
            update_data = {
                'status': status,
                'total_pages_scraped': self.stats.total_pages,
                'total_contractors_found': self.stats.total_contractors,
                'new_contractors_added': self.stats.total_contractors,
                'errors_encountered': self.stats.errors,
                'notes': f'FireCrawl: {self.stats.firecrawl_requests}, Crawl4AI: {self.stats.crawl4ai_requests}, LLM: {self.stats.llm_requests}, Screenshots: {self.stats.screenshot_analyses}, PDFs: {self.stats.pdf_processed}, DOCX: {self.stats.docx_processed}'
            }
            
            if end_time:
                update_data['end_time'] = end_time.isoformat()
            
            supabase.table('scraping_runs').update(update_data).eq('run_id', self.stats.run_id).execute()
            logger.info(f"📊 Updated scraping run {self.stats.run_id} with status: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update scraping run: {str(e)}")
    
    async def firecrawl_scrape(self, url: str, extract_schema: Optional[Dict] = None) -> Dict:
        """Scrape using FireCrawl API with rate limiting"""
        if not firecrawl_app:
            logger.warning("FireCrawl API key not configured")
            return {}
        
        try:
            # Apply rate limiting
            rate_limited = await self.rate_limiter.wait_if_needed()
            if rate_limited:
                self.stats.rate_limit_hits += 1
            
            logger.info(f"🔥 FireCrawl scraping: {url}")
            
            # Updated FireCrawl v1 API parameters
            scrape_params = {
                "formats": ["markdown", "extract", "screenshot"],
                "onlyMainContent": True
            }

            if extract_schema:
                scrape_params["extract"] = {
                    "schema": extract_schema,
                    "prompt": "Extract all CIDB contractor information including contact details"
                }

            result = firecrawl_app.scrape_url(url, scrape_params)
            self.stats.firecrawl_requests += 1
            
            # Save screenshot if available
            if result.get('screenshot'):
                screenshot_path = self.screenshots_dir / f"firecrawl_{int(time.time())}.png"
                with open(screenshot_path, 'wb') as f:
                    f.write(base64.b64decode(result['screenshot']))
                logger.info(f"📸 Saved FireCrawl screenshot: {screenshot_path}")
            
            return result
            
        except Exception as e:
            logger.error(f"FireCrawl scraping failed: {str(e)}")
            self.stats.errors.append(f"FireCrawl error: {str(e)}")
            return {}
    
    async def crawl4ai_scrape(self, url: str) -> Dict:
        """Scrape using Crawl4AI with advanced extraction"""
        try:
            logger.info(f"🕷️ Crawl4AI scraping: {url}")
            
            # Enhanced extraction schema
            extraction_schema = {
                "name": "CIDB Contractors Enhanced",
                "baseSelector": "table tr:not(:first-child), .contractor-row, .data-row",
                "fields": [
                    {"name": "crs_number", "selector": "td:nth-child(1), .crs-number", "type": "text"},
                    {"name": "contractor_name", "selector": "td:nth-child(2), .contractor-name", "type": "text"},
                    {"name": "status", "selector": "td:nth-child(3), .status", "type": "text"},
                    {"name": "grading", "selector": "td:nth-child(4), .grading", "type": "text"},
                    {"name": "expiry_date", "selector": "td:nth-child(5), .expiry-date", "type": "text"},
                    {"name": "trading_name", "selector": "td:nth-child(6), .trading-name", "type": "text"},
                    {"name": "province", "selector": "td:nth-child(7), .province", "type": "text"},
                    {"name": "city", "selector": "td:nth-child(8), .city", "type": "text"},
                    {"name": "bbbee_status", "selector": "td:nth-child(9), .bbbee-status", "type": "text"},
                    {"name": "potentially_emerging", "selector": "td:nth-child(10), .emerging", "type": "text"},
                    {"name": "contact_number", "selector": "td:nth-child(11), .contact, .phone", "type": "text"},
                    {"name": "email_address", "selector": "td:nth-child(12), .email", "type": "text"}
                ]
            }
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                extraction_strategy = JsonCssExtractionStrategy(extraction_schema, verbose=True)
                
                config = CrawlerRunConfig(
                    extraction_strategy=extraction_strategy,
                    cache_mode=CacheMode.BYPASS,
                    page_timeout=30000,
                    wait_for="css:body",
                    screenshot=True
                )
                
                result = await crawler.arun(url=url, config=config)
                self.stats.crawl4ai_requests += 1
                
                # Save screenshot
                if result.screenshot:
                    screenshot_path = self.screenshots_dir / f"crawl4ai_{int(time.time())}.png"
                    with open(screenshot_path, 'wb') as f:
                        f.write(base64.b64decode(result.screenshot))
                    logger.info(f"📸 Saved Crawl4AI screenshot: {screenshot_path}")
                
                return {
                    'success': result.success,
                    'markdown': result.markdown,
                    'extracted_content': result.extracted_content,
                    'screenshot': result.screenshot,
                    'html': result.html
                }
                
        except Exception as e:
            logger.error(f"Crawl4AI scraping failed: {str(e)}")
            self.stats.errors.append(f"Crawl4AI error: {str(e)}")
            return {}
    
    async def llm_extract_from_content(self, content: str, content_type: str = "html") -> Dict:
        """Extract contractor data using LLM analysis"""
        try:
            logger.info(f"🤖 LLM extracting from {content_type} content ({len(content)} chars)")
            
            prompt = f"""
            Extract CIDB contractor information from the following {content_type} content.
            Look for contractor registration numbers (CRS), company names, contact details, 
            grading information, and any other relevant contractor data.
            
            Return the data in JSON format matching this schema:
            {{
                "contractors": [
                    {{
                        "crs_number": "string",
                        "contractor_name": "string",
                        "status": "string",
                        "grading": "string",
                        "expiry_date": "string",
                        "trading_name": "string",
                        "province": "string",
                        "city": "string",
                        "bbbee_status": "string",
                        "potentially_emerging": "string",
                        "contact_number": "string",
                        "email_address": "string"
                    }}
                ],
                "total_found": 0
            }}
            
            Content to analyze:
            {content[:8000]}  # Limit content to avoid token limits
            """
            
            # Try OpenAI first, then Anthropic
            if openai_client:
                try:
                    response = openai_client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=[
                            {"role": "system", "content": "You are an expert at extracting structured data from web content. Always return valid JSON."},
                            {"role": "user", "content": prompt}
                        ],
                        response_format={"type": "json_object"},
                        temperature=0
                    )
                    
                    result = json.loads(response.choices[0].message.content)
                    self.stats.llm_requests += 1
                    logger.info(f"✅ OpenAI extracted {result.get('total_found', 0)} contractors")
                    return result
                    
                except Exception as e:
                    logger.warning(f"OpenAI extraction failed: {str(e)}")
            
            if anthropic_client:
                try:
                    response = anthropic_client.messages.create(
                        model="claude-3-haiku-20240307",
                        max_tokens=4000,
                        messages=[
                            {"role": "user", "content": prompt}
                        ]
                    )
                    
                    result = json.loads(response.content[0].text)
                    self.stats.llm_requests += 1
                    logger.info(f"✅ Claude extracted {result.get('total_found', 0)} contractors")
                    return result
                    
                except Exception as e:
                    logger.warning(f"Claude extraction failed: {str(e)}")
            
            logger.warning("No LLM clients available for extraction")
            return {"contractors": [], "total_found": 0}
            
        except Exception as e:
            logger.error(f"LLM extraction failed: {str(e)}")
            self.stats.errors.append(f"LLM error: {str(e)}")
            return {"contractors": [], "total_found": 0}

    async def screenshot_analysis(self, screenshot_path: str) -> Dict:
        """Analyze screenshot using OCR and LLM"""
        try:
            logger.info(f"📸 Analyzing screenshot: {screenshot_path}")

            # OCR extraction
            image = Image.open(screenshot_path)
            ocr_text = pytesseract.image_to_string(image)

            if not ocr_text.strip():
                logger.warning("No text extracted from screenshot")
                return {"contractors": [], "total_found": 0}

            # Use LLM to analyze OCR text
            result = await self.llm_extract_from_content(ocr_text, "OCR screenshot")
            self.stats.screenshot_analyses += 1

            return result

        except Exception as e:
            logger.error(f"Screenshot analysis failed: {str(e)}")
            self.stats.errors.append(f"Screenshot analysis error: {str(e)}")
            return {"contractors": [], "total_found": 0}

    async def process_pdf(self, pdf_path: str) -> Dict:
        """Process PDF document and extract contractor data"""
        try:
            logger.info(f"📄 Processing PDF: {pdf_path}")

            text_content = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"

            if not text_content.strip():
                logger.warning("No text extracted from PDF")
                return {"contractors": [], "total_found": 0}

            result = await self.llm_extract_from_content(text_content, "PDF")
            self.stats.pdf_processed += 1

            return result

        except Exception as e:
            logger.error(f"PDF processing failed: {str(e)}")
            self.stats.errors.append(f"PDF processing error: {str(e)}")
            return {"contractors": [], "total_found": 0}

    async def process_docx(self, docx_path: str) -> Dict:
        """Process DOCX document and extract contractor data"""
        try:
            logger.info(f"📝 Processing DOCX: {docx_path}")

            doc = docx.Document(docx_path)
            text_content = ""

            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"

            # Extract table data
            for table in doc.tables:
                for row in table.rows:
                    row_text = " | ".join([cell.text for cell in row.cells])
                    text_content += row_text + "\n"

            if not text_content.strip():
                logger.warning("No text extracted from DOCX")
                return {"contractors": [], "total_found": 0}

            result = await self.llm_extract_from_content(text_content, "DOCX")
            self.stats.docx_processed += 1

            return result

        except Exception as e:
            logger.error(f"DOCX processing failed: {str(e)}")
            self.stats.errors.append(f"DOCX processing error: {str(e)}")
            return {"contractors": [], "total_found": 0}

    async def process_file(self, file_path: str) -> Dict:
        """Process any supported file format"""
        file_path = Path(file_path)

        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return {"contractors": [], "total_found": 0}

        mime_type, _ = mimetypes.guess_type(str(file_path))
        extension = file_path.suffix.lower()

        logger.info(f"🔍 Processing file: {file_path} (type: {mime_type}, ext: {extension})")

        if extension == '.pdf' or mime_type == 'application/pdf':
            return await self.process_pdf(str(file_path))
        elif extension in ['.docx', '.doc'] or mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return await self.process_docx(str(file_path))
        elif extension in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'] or mime_type and mime_type.startswith('image/'):
            return await self.screenshot_analysis(str(file_path))
        else:
            # Try to read as text
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return await self.llm_extract_from_content(content, f"text file ({extension})")
            except Exception as e:
                logger.error(f"Unsupported file format: {file_path} - {str(e)}")
                return {"contractors": [], "total_found": 0}

    async def save_contractors_to_supabase(self, contractors: List[Dict], source: str):
        """Save contractors to Supabase with source tracking"""
        if not contractors:
            return

        try:
            processed_contractors = []

            for i, contractor in enumerate(contractors):
                processed_contractor = {
                    'crs_number': contractor.get('crs_number') or f'HYBRID_{source}_{i+1}',
                    'contractor_name': contractor.get('contractor_name') or 'Unknown',
                    'status': contractor.get('status'),
                    'grading': contractor.get('grading'),
                    'expiry_date': contractor.get('expiry_date'),
                    'trading_name': contractor.get('trading_name'),
                    'province': contractor.get('province'),
                    'city': contractor.get('city'),
                    'bbbee_status': contractor.get('bbbee_status'),
                    'potentially_emerging': contractor.get('potentially_emerging'),
                    'contact_number': contractor.get('contact_number'),
                    'email_address': contractor.get('email_address'),
                    'contractor_status': f'hybrid_{source}',
                    'scraped_at': datetime.utcnow().isoformat() + "Z"
                }

                # Only add if we have meaningful data
                if any(v and str(v).strip() and len(str(v).strip()) > 1
                      for v in processed_contractor.values() if v):
                    processed_contractors.append(processed_contractor)

            if processed_contractors:
                # Batch insert
                batch_size = 100
                for i in range(0, len(processed_contractors), batch_size):
                    batch = processed_contractors[i:i+batch_size]

                    result = supabase.table('contractors').upsert(
                        batch,
                        on_conflict='crs_number,contractor_status'
                    ).execute()

                    if not hasattr(result, 'error') or not result.error:
                        logger.info(f"✅ Saved {len(batch)} contractors from {source} to database")
                        self.stats.total_contractors += len(batch)
                    else:
                        logger.error(f"❌ Database save error: {result.error}")

        except Exception as e:
            logger.error(f"Error saving contractors from {source}: {str(e)}")
            self.stats.errors.append(f"Save error ({source}): {str(e)}")

    async def hybrid_scrape_url(self, url: str) -> Dict:
        """Perform hybrid scraping using all available methods"""
        logger.info(f"🔄 Starting hybrid scrape of: {url}")

        all_contractors = []
        methods_used = []

        # Method 1: FireCrawl API
        if firecrawl_app:
            try:
                firecrawl_result = await self.firecrawl_scrape(url, ContractorList.model_json_schema())
                if firecrawl_result.get('extract', {}).get('contractors'):
                    contractors = firecrawl_result['extract']['contractors']
                    all_contractors.extend(contractors)
                    methods_used.append(f"FireCrawl ({len(contractors)} contractors)")
                    await self.save_contractors_to_supabase(contractors, "firecrawl")
            except Exception as e:
                logger.warning(f"FireCrawl method failed: {str(e)}")

        # Method 2: Crawl4AI
        try:
            crawl4ai_result = await self.crawl4ai_scrape(url)
            if crawl4ai_result.get('extracted_content'):
                extracted_data = json.loads(crawl4ai_result['extracted_content'])
                if extracted_data:
                    all_contractors.extend(extracted_data)
                    methods_used.append(f"Crawl4AI ({len(extracted_data)} contractors)")
                    await self.save_contractors_to_supabase(extracted_data, "crawl4ai")

            # Method 3: LLM analysis of Crawl4AI content
            if crawl4ai_result.get('markdown'):
                llm_result = await self.llm_extract_from_content(crawl4ai_result['markdown'], "markdown")
                if llm_result.get('contractors'):
                    contractors = llm_result['contractors']
                    all_contractors.extend(contractors)
                    methods_used.append(f"LLM-Markdown ({len(contractors)} contractors)")
                    await self.save_contractors_to_supabase(contractors, "llm_markdown")

        except Exception as e:
            logger.warning(f"Crawl4AI method failed: {str(e)}")

        # Method 4: Screenshot analysis (if screenshots were captured)
        screenshot_files = list(self.screenshots_dir.glob("*.png"))
        for screenshot_file in screenshot_files[-2:]:  # Analyze last 2 screenshots
            try:
                screenshot_result = await self.screenshot_analysis(str(screenshot_file))
                if screenshot_result.get('contractors'):
                    contractors = screenshot_result['contractors']
                    all_contractors.extend(contractors)
                    methods_used.append(f"Screenshot ({len(contractors)} contractors)")
                    await self.save_contractors_to_supabase(contractors, "screenshot")
            except Exception as e:
                logger.warning(f"Screenshot analysis failed: {str(e)}")

        logger.info(f"🎯 Hybrid scrape completed. Methods used: {', '.join(methods_used)}")

        return {
            'total_contractors': len(all_contractors),
            'contractors': all_contractors,
            'methods_used': methods_used,
            'url': url
        }

    async def run_super_hybrid_scrape(self, targets: List[Union[str, Path]] = None):
        """Run the complete super hybrid scraping process"""
        start_time = datetime.utcnow()

        try:
            # Initialize scraping run
            await self.initialize_scraping_run('super_hybrid')

            logger.info("🚀 Starting SUPER HYBRID CIDB contractor scraping...")

            # Default targets if none provided
            if not targets:
                targets = [
                    "https://portal.cidb.org.za/RegisterOfContractors/",
                    "http://registers.cidb.org.za/PublicContractors/ContractorSearch",
                    "https://www.cidb.org.za/"
                ]

            all_results = []

            for target in targets:
                try:
                    if isinstance(target, (str, Path)) and str(target).startswith(('http://', 'https://')):
                        # URL scraping
                        result = await self.hybrid_scrape_url(str(target))
                        all_results.append(result)
                        self.stats.total_pages += 1
                    else:
                        # File processing
                        result = await self.process_file(str(target))
                        if result.get('contractors'):
                            await self.save_contractors_to_supabase(result['contractors'], "file")
                            all_results.append({
                                'total_contractors': len(result['contractors']),
                                'contractors': result['contractors'],
                                'methods_used': [f"File processing ({Path(target).suffix})"],
                                'source': str(target)
                            })

                    # Respect rate limits
                    await asyncio.sleep(RATE_LIMIT_DELAY)

                except Exception as e:
                    logger.error(f"Failed to process target {target}: {str(e)}")
                    self.stats.errors.append(f"Target {target} failed: {str(e)}")

            # Update final statistics
            end_time = datetime.utcnow()

            if self.stats.errors:
                status = 'partial' if self.stats.total_contractors > 0 else 'failed'
            else:
                status = 'completed'

            await self.update_scraping_run(status, end_time)

            # Log comprehensive results
            duration = (end_time - start_time).total_seconds()
            logger.info(f"""
🎉 SUPER HYBRID SCRAPING COMPLETED in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Status: {status}
Total Contractors: {self.stats.total_contractors}
FireCrawl Requests: {self.stats.firecrawl_requests}
Crawl4AI Requests: {self.stats.crawl4ai_requests}
LLM Requests: {self.stats.llm_requests}
Screenshot Analyses: {self.stats.screenshot_analyses}
PDFs Processed: {self.stats.pdf_processed}
DOCX Processed: {self.stats.docx_processed}
Rate Limit Hits: {self.stats.rate_limit_hits}
Errors: {len(self.stats.errors)}
            """)

            if self.stats.errors:
                logger.warning(f"Errors encountered: {self.stats.errors}")

            return {
                'run_id': self.stats.run_id,
                'status': status,
                'total_contractors': self.stats.total_contractors,
                'duration_seconds': duration,
                'results': all_results,
                'stats': {
                    'firecrawl_requests': self.stats.firecrawl_requests,
                    'crawl4ai_requests': self.stats.crawl4ai_requests,
                    'llm_requests': self.stats.llm_requests,
                    'screenshot_analyses': self.stats.screenshot_analyses,
                    'pdf_processed': self.stats.pdf_processed,
                    'docx_processed': self.stats.docx_processed,
                    'rate_limit_hits': self.stats.rate_limit_hits
                },
                'errors': self.stats.errors
            }

        except Exception as e:
            logger.error(f"Critical error during super hybrid scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise

async def main():
    """Main entry point for super hybrid scraper"""
    scraper = SuperHybridScraper()

    try:
        # You can specify custom targets here
        # targets = ["path/to/document.pdf", "https://example.com", "screenshot.png"]
        result = await scraper.run_super_hybrid_scrape()
        print(f"🎉 Super hybrid scraping completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Super hybrid scraping failed: {str(e)}")
        print(f"❌ Super hybrid scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
