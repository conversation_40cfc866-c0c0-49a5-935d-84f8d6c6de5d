#!/usr/bin/env python3
"""
Test script for Super Hybrid CIDB Scraper
Validates all components and methods
"""

import asyncio
import json
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_environment():
    """Test environment configuration"""
    print("🔧 Testing environment configuration...")
    
    # Check .env file
    if not Path('.env').exists():
        print("❌ .env file not found. Please run setup_super_hybrid.py first")
        return False
    
    # Check required environment variables
    required_vars = ['SUPABASE_URL', 'SUPABASE_KEY']
    optional_vars = ['FIRECRAWL_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY']
    
    from dotenv import load_dotenv
    load_dotenv()
    
    missing_required = []
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
    
    if missing_required:
        print(f"❌ Missing required environment variables: {missing_required}")
        return False
    
    print("✅ Required environment variables found")
    
    # Check optional variables
    available_optional = [var for var in optional_vars if os.getenv(var)]
    print(f"✅ Available optional APIs: {available_optional}")
    
    return True

async def test_imports():
    """Test all required imports"""
    print("\n📦 Testing imports...")
    
    try:
        from super_hybrid_scraper import SuperHybridScraper, ContractorData, ContractorList
        print("✅ Super hybrid scraper imports successful")
        
        # Test individual components
        import crawl4ai
        print("✅ Crawl4AI imported")
        
        try:
            import firecrawl
            print("✅ FireCrawl imported")
        except ImportError:
            print("⚠️ FireCrawl not available (install with: pip install firecrawl-py)")
        
        try:
            import openai
            print("✅ OpenAI imported")
        except ImportError:
            print("⚠️ OpenAI not available")
        
        try:
            import anthropic
            print("✅ Anthropic imported")
        except ImportError:
            print("⚠️ Anthropic not available")
        
        import PyPDF2
        print("✅ PyPDF2 imported")
        
        import docx
        print("✅ python-docx imported")
        
        import pytesseract
        print("✅ pytesseract imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

async def test_scraper_initialization():
    """Test scraper initialization"""
    print("\n🚀 Testing scraper initialization...")
    
    try:
        from super_hybrid_scraper import SuperHybridScraper
        scraper = SuperHybridScraper()
        
        print(f"✅ Scraper initialized with run ID: {scraper.stats.run_id}")
        print(f"✅ Output directory created: {scraper.output_dir}")
        print(f"✅ Screenshots directory: {scraper.screenshots_dir}")
        print(f"✅ Documents directory: {scraper.documents_dir}")
        
        return scraper
        
    except Exception as e:
        print(f"❌ Scraper initialization failed: {e}")
        return None

async def test_database_connection(scraper):
    """Test database connection"""
    print("\n🗄️ Testing database connection...")
    
    try:
        # Test database connection
        await scraper.initialize_scraping_run('test')
        print("✅ Database connection successful")
        
        # Update run as completed
        await scraper.update_scraping_run('completed')
        print("✅ Database update successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_llm_extraction(scraper):
    """Test LLM extraction capabilities"""
    print("\n🤖 Testing LLM extraction...")
    
    # Sample contractor data for testing
    test_content = """
    CIDB Contractor Information:
    
    CRS Number: 12345/CE/2024
    Contractor Name: ABC Construction (Pty) Ltd
    Status: Active
    Grading: 9CE
    Expiry Date: 2025-12-31
    Trading Name: ABC Builders
    Province: Gauteng
    City: Johannesburg
    B-BBEE Status: Level 4
    Potentially Emerging: Yes
    Contact: 011-123-4567
    Email: <EMAIL>
    
    CRS Number: 67890/GB/2024
    Contractor Name: XYZ Engineering Solutions
    Status: Active
    Grading: 7GB
    Expiry Date: 2025-06-30
    Province: Western Cape
    City: Cape Town
    Contact: 021-987-6543
    Email: <EMAIL>
    """
    
    try:
        result = await scraper.llm_extract_from_content(test_content, "test data")
        
        if result.get('contractors'):
            contractors = result['contractors']
            print(f"✅ LLM extraction successful: {len(contractors)} contractors found")
            
            # Display first contractor
            if contractors:
                first_contractor = contractors[0]
                print(f"   Sample: {first_contractor.get('contractor_name', 'Unknown')}")
                print(f"   CRS: {first_contractor.get('crs_number', 'Unknown')}")
                print(f"   Contact: {first_contractor.get('contact_number', 'Unknown')}")
            
            return True
        else:
            print("⚠️ LLM extraction returned no contractors")
            return False
            
    except Exception as e:
        print(f"❌ LLM extraction failed: {e}")
        return False

async def test_file_processing(scraper):
    """Test file processing capabilities"""
    print("\n📄 Testing file processing...")
    
    # Create a test text file
    test_file = Path("test_contractor_data.txt")
    test_content = """
    Contractor Registry Report
    
    1. ABC Construction (Pty) Ltd
       CRS: 12345/CE/2024
       Status: Active
       Contact: 011-123-4567
       Email: <EMAIL>
    
    2. XYZ Engineering
       CRS: 67890/GB/2024
       Status: Active
       Contact: 021-987-6543
       Email: <EMAIL>
    """
    
    try:
        # Write test file
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        # Process the file
        result = await scraper.process_file(str(test_file))
        
        if result.get('contractors'):
            print(f"✅ File processing successful: {len(result['contractors'])} contractors found")
            
            # Clean up
            test_file.unlink()
            
            return True
        else:
            print("⚠️ File processing returned no contractors")
            test_file.unlink()
            return False
            
    except Exception as e:
        print(f"❌ File processing failed: {e}")
        if test_file.exists():
            test_file.unlink()
        return False

async def test_rate_limiter(scraper):
    """Test rate limiting functionality"""
    print("\n⏱️ Testing rate limiter...")
    
    try:
        # Test rate limiter
        import time
        start_time = time.time()
        
        # This should add a delay
        await scraper.rate_limiter.wait_if_needed()
        
        elapsed = time.time() - start_time
        print(f"✅ Rate limiter working (delay: {elapsed:.2f}s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Rate limiter test failed: {e}")
        return False

async def test_crawl4ai_basic(scraper):
    """Test basic Crawl4AI functionality"""
    print("\n🕷️ Testing Crawl4AI basic functionality...")
    
    try:
        # Test with a simple, reliable URL
        test_url = "https://httpbin.org/html"
        
        result = await scraper.crawl4ai_scrape(test_url)
        
        if result.get('success'):
            print("✅ Crawl4AI basic test successful")
            print(f"   Content length: {len(result.get('markdown', ''))}")
            return True
        else:
            print("⚠️ Crawl4AI basic test failed")
            return False
            
    except Exception as e:
        print(f"❌ Crawl4AI test failed: {e}")
        return False

async def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🧪 SUPER HYBRID SCRAPER - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    test_results = {}
    
    # Test 1: Environment
    test_results['environment'] = await test_environment()
    
    # Test 2: Imports
    test_results['imports'] = await test_imports()
    
    if not test_results['imports']:
        print("\n❌ Cannot continue without proper imports")
        return test_results
    
    # Test 3: Scraper initialization
    scraper = await test_scraper_initialization()
    test_results['initialization'] = scraper is not None
    
    if not scraper:
        print("\n❌ Cannot continue without scraper initialization")
        return test_results
    
    # Test 4: Database connection
    test_results['database'] = await test_database_connection(scraper)
    
    # Test 5: LLM extraction
    test_results['llm_extraction'] = await test_llm_extraction(scraper)
    
    # Test 6: File processing
    test_results['file_processing'] = await test_file_processing(scraper)
    
    # Test 7: Rate limiter
    test_results['rate_limiter'] = await test_rate_limiter(scraper)
    
    # Test 8: Crawl4AI basic
    test_results['crawl4ai_basic'] = await test_crawl4ai_basic(scraper)
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name.replace('_', ' ').title()}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Super Hybrid Scraper is ready for use!")
        print("\n🚀 Next steps:")
        print("   1. Run: python3 super_hybrid_scraper.py")
        print("   2. Check results in Supabase dashboard")
        print("   3. Review SUPER_HYBRID_GUIDE.md for advanced usage")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration and try again.")
        print("   1. Ensure all API keys are set in .env file")
        print("   2. Run: python3 setup_super_hybrid.py")
        print("   3. Check the installation guide")
    
    return test_results

async def main():
    """Main test function"""
    try:
        results = await run_comprehensive_test()
        return results
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test suite failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
