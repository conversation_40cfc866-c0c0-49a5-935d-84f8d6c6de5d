#!/usr/bin/env python3
"""
Inspect CIDB site structure to understand the page layout
"""

import asyncio
from crawl4ai import Async<PERSON>ebCraw<PERSON>, BrowserConfig, CrawlerRunConfig, CacheMode

async def inspect_cidb_site():
    """Inspect the CIDB site structure"""
    print("🔍 Inspecting CIDB site structure...")
    
    browser_config = BrowserConfig(headless=True, verbose=True)
    config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(
            url="https://portal.cidb.org.za/RegisterOfContractors/",
            config=config
        )
        
        if result.success:
            print("✅ Successfully loaded CIDB page")
            
            # Save the HTML for inspection
            with open("cidb_page.html", "w", encoding="utf-8") as f:
                f.write(result.html)
            print("📄 HTML saved to cidb_page.html")
            
            # Save the markdown for inspection
            with open("cidb_page.md", "w", encoding="utf-8") as f:
                f.write(result.markdown)
            print("📄 Markdown saved to cidb_page.md")
            
            # Look for key elements in the HTML
            html_lower = result.html.lower()
            
            print("\n🔍 Looking for key elements:")
            
            # Check for various possible table IDs/classes
            table_indicators = [
                'gvcontractors',
                'contractor',
                'table',
                'gridview',
                'datagrid',
                'contentplaceholder1'
            ]
            
            for indicator in table_indicators:
                if indicator in html_lower:
                    print(f"✅ Found '{indicator}' in HTML")
                else:
                    print(f"❌ '{indicator}' not found in HTML")
            
            # Check for radio buttons
            radio_indicators = [
                'rbactive',
                'rbinactive',
                'radio',
                'active',
                'inactive'
            ]
            
            print("\n🔘 Looking for radio button elements:")
            for indicator in radio_indicators:
                if indicator in html_lower:
                    print(f"✅ Found '{indicator}' in HTML")
                else:
                    print(f"❌ '{indicator}' not found in HTML")
            
            # Look for forms
            if 'form' in html_lower:
                print("✅ Found form elements")
            else:
                print("❌ No form elements found")
            
            return True
        else:
            print(f"❌ Failed to load CIDB page: {result.error_message}")
            return False

if __name__ == "__main__":
    asyncio.run(inspect_cidb_site())
