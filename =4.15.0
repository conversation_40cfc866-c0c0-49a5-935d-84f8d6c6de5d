Defaulting to user installation because normal site-packages is not writeable
Collecting selenium
  Downloading selenium-4.34.2-py3-none-any.whl.metadata (7.5 kB)
Requirement already satisfied: urllib3~=2.5.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from urllib3[socks]~=2.5.0->selenium) (2.5.0)
Collecting trio~=0.30.0 (from selenium)
  Downloading trio-0.30.0-py3-none-any.whl.metadata (8.5 kB)
Collecting trio-websocket~=0.12.2 (from selenium)
  Downloading trio_websocket-0.12.2-py3-none-any.whl.metadata (5.1 kB)
Requirement already satisfied: certifi>=2025.6.15 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from selenium) (2025.7.9)
Requirement already satisfied: typing_extensions~=4.14.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from selenium) (4.14.1)
Collecting websocket-client~=1.8.0 (from selenium)
  Downloading websocket_client-1.8.0-py3-none-any.whl.metadata (8.0 kB)
Requirement already satisfied: attrs>=23.2.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from trio~=0.30.0->selenium) (25.3.0)
Collecting sortedcontainers (from trio~=0.30.0->selenium)
  Downloading sortedcontainers-2.4.0-py2.py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: idna in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from trio~=0.30.0->selenium) (3.10)
Collecting outcome (from trio~=0.30.0->selenium)
  Downloading outcome-1.3.0.post0-py2.py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: sniffio>=1.3.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from trio~=0.30.0->selenium) (1.3.1)
Requirement already satisfied: exceptiongroup in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from trio~=0.30.0->selenium) (1.3.0)
Collecting wsproto>=0.14 (from trio-websocket~=0.12.2->selenium)
  Downloading wsproto-1.2.0-py3-none-any.whl.metadata (5.6 kB)
Collecting pysocks!=1.5.7,<2.0,>=1.5.6 (from urllib3[socks]~=2.5.0->selenium)
  Downloading PySocks-1.7.1-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: h11<1,>=0.9.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from wsproto>=0.14->trio-websocket~=0.12.2->selenium) (0.16.0)
Downloading selenium-4.34.2-py3-none-any.whl (9.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 9.4/9.4 MB 9.5 MB/s eta 0:00:00
Downloading trio-0.30.0-py3-none-any.whl (499 kB)
Downloading trio_websocket-0.12.2-py3-none-any.whl (21 kB)
Downloading PySocks-1.7.1-py3-none-any.whl (16 kB)
Downloading websocket_client-1.8.0-py3-none-any.whl (58 kB)
Downloading outcome-1.3.0.post0-py2.py3-none-any.whl (10 kB)
Downloading wsproto-1.2.0-py3-none-any.whl (24 kB)
Downloading sortedcontainers-2.4.0-py2.py3-none-any.whl (29 kB)
Installing collected packages: sortedcontainers, wsproto, websocket-client, pysocks, outcome, trio, trio-websocket, selenium

Successfully installed outcome-1.3.0.post0 pysocks-1.7.1 selenium-4.34.2 sortedcontainers-2.4.0 trio-0.30.0 trio-websocket-0.12.2 websocket-client-1.8.0 wsproto-1.2.0
