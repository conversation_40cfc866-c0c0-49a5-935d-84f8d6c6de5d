Defaulting to user installation because normal site-packages is not writeable
Collecting anthropic
  Downloading anthropic-0.57.1-py3-none-any.whl.metadata (27 kB)
Requirement already satisfied: anyio<5,>=3.5.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (1.9.0)
Requirement already satisfied: httpx<1,>=0.25.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (0.10.0)
Requirement already satisfied: pydantic<3,>=1.9.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (2.11.7)
Requirement already satisfied: sniffio in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (1.3.1)
Requirement already satisfied: typing-extensions<5,>=4.10 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anthropic) (4.14.1)
Requirement already satisfied: exceptiongroup>=1.0.2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anyio<5,>=3.5.0->anthropic) (1.3.0)
Requirement already satisfied: idna>=2.8 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from anyio<5,>=3.5.0->anthropic) (3.10)
Requirement already satisfied: certifi in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from httpx<1,>=0.25.0->anthropic) (2025.7.9)
Requirement already satisfied: httpcore==1.* in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from httpx<1,>=0.25.0->anthropic) (1.0.9)
Requirement already satisfied: h11>=0.16 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from httpcore==1.*->httpx<1,>=0.25.0->anthropic) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pydantic<3,>=1.9.0->anthropic) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pydantic<3,>=1.9.0->anthropic) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from pydantic<3,>=1.9.0->anthropic) (0.4.1)
Downloading anthropic-0.57.1-py3-none-any.whl (292 kB)
Installing collected packages: anthropic
Successfully installed anthropic-0.57.1
