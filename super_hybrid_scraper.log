2025-07-10 16:06:38,846 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 16:06:38,850 - INFO - 🚀 Initialized hybrid scraping run: 0c90e1ae-2055-4c60-b575-0c56878cfa37
2025-07-10 16:06:39,396 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.0c90e1ae-2055-4c60-b575-0c56878cfa37 "HTTP/2 200 OK"
2025-07-10 16:06:39,398 - INFO - 📊 Updated scraping run 0c90e1ae-2055-4c60-b575-0c56878cfa37 with status: completed
2025-07-10 16:06:39,398 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:06:46,645 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156406.png
2025-07-10 16:07:56,290 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 16:07:56,292 - ERROR - Failed to initialize scraping run: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (12, 31bf2c4d-9433-4c13-aebe-f0c8d85d4c84, started, demo, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:07:56.172548+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:07:56,292 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:08:00,983 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156480.png
2025-07-10 16:08:01,056 - INFO - 🕷️ Crawl4AI scraping: https://example.com
2025-07-10 16:08:02,961 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156482.png
2025-07-10 16:08:03,049 - INFO - 🔍 Processing file: demo_contractors.txt (type: text/plain, ext: .txt)
2025-07-10 16:08:03,049 - INFO - 🤖 LLM extracting from text file (.txt) content (619 chars)
2025-07-10 16:08:03,049 - WARNING - No LLM clients available for extraction
2025-07-10 16:08:09,053 - INFO - 🔄 Starting hybrid scrape of: https://httpbin.org/html
2025-07-10 16:08:09,053 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:08:38,359 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156518.png
2025-07-10 16:08:38,436 - INFO - 🤖 LLM extracting from markdown content (3598 chars)
2025-07-10 16:08:38,436 - WARNING - No LLM clients available for extraction
2025-07-10 16:08:38,437 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/crawl4ai_1752156480.png
2025-07-10 16:08:38,929 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:08:38,929 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/crawl4ai_1752156186.png
2025-07-10 16:08:38,989 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:08:38,989 - INFO - 🎯 Hybrid scrape completed. Methods used: 
2025-07-10 16:19:46,329 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 16:19:46,331 - ERROR - Failed to initialize scraping run: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (13, 3d69c548-7b1f-44ed-abc6-74a27f98e173, started, cidb_pagination, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:19:46.244773+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:19:46,331 - ERROR - Critical error during CIDB pagination scraping: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (13, 3d69c548-7b1f-44ed-abc6-74a27f98e173, started, cidb_pagination, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:19:46.244773+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:19:46,886 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.3d69c548-7b1f-44ed-abc6-74a27f98e173 "HTTP/2 200 OK"
2025-07-10 16:19:46,894 - INFO - 📊 Updated scraping run 3d69c548-7b1f-44ed-abc6-74a27f98e173 with status: failed
2025-07-10 16:19:46,894 - ERROR - CIDB pagination scraping failed: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (13, 3d69c548-7b1f-44ed-abc6-74a27f98e173, started, cidb_pagination, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:19:46.244773+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:20:50,460 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 16:20:50,466 - INFO - 🚀 Initialized hybrid scraping run: cd7e030d-5ce3-4ae4-9030-5de03752bf66
2025-07-10 16:20:50,466 - INFO - 🚀 Starting CIDB pagination scraping for 10 pages...
2025-07-10 16:20:50,466 - INFO - 🎯 Target URL: https://portal.cidb.org.za/RegisterOfContractors/
2025-07-10 16:20:51,317 - INFO - 
📄 SCRAPING PAGE 1/10
2025-07-10 16:20:51,317 - INFO - ==================================================
2025-07-10 16:20:51,317 - INFO - 🔍 Extracting data from page 1...
2025-07-10 16:20:54,655 - INFO - 📸 Saved page 1 screenshot: scraper_output/screenshots/cidb_page_1_1752157254.png
2025-07-10 16:20:54,655 - INFO - 🤖 LLM extracting from CIDB page 1 content content (26295 chars)
2025-07-10 16:20:54,655 - WARNING - No LLM clients available for extraction
2025-07-10 16:20:54,655 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_1_1752157254.png
2025-07-10 16:20:54,673 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:20:54,673 - INFO - 🎯 Page 1: Found 0 unique contractors
2025-07-10 16:20:54,674 - INFO - 📊 Page 1 Summary:
2025-07-10 16:20:54,674 - INFO -    Contractors found: 0
2025-07-10 16:20:54,674 - INFO -    Total so far: 0
2025-07-10 16:20:54,674 - INFO -    Pages completed: 1/10
2025-07-10 16:20:54,674 - INFO - 🔄 Navigating to page 2...
2025-07-10 16:20:54,674 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:20:56,764 - INFO - ✅ Successfully navigated to page 2 using Page Number Link
2025-07-10 16:20:59,765 - INFO - 
📄 SCRAPING PAGE 2/10
2025-07-10 16:20:59,766 - INFO - ==================================================
2025-07-10 16:20:59,766 - INFO - 🔍 Extracting data from page 2...
2025-07-10 16:21:01,520 - INFO - 📸 Saved page 2 screenshot: scraper_output/screenshots/cidb_page_2_1752157261.png
2025-07-10 16:21:01,520 - INFO - 🤖 LLM extracting from CIDB page 2 content content (26295 chars)
2025-07-10 16:21:01,520 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:01,521 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_2_1752157261.png
2025-07-10 16:21:01,534 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:01,535 - INFO - 🎯 Page 2: Found 0 unique contractors
2025-07-10 16:21:01,535 - INFO - 📊 Page 2 Summary:
2025-07-10 16:21:01,535 - INFO -    Contractors found: 0
2025-07-10 16:21:01,535 - INFO -    Total so far: 0
2025-07-10 16:21:01,535 - INFO -    Pages completed: 2/10
2025-07-10 16:21:01,535 - INFO - 🔄 Navigating to page 3...
2025-07-10 16:21:01,535 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:03,699 - INFO - ✅ Successfully navigated to page 3 using Page Number Link
2025-07-10 16:21:06,701 - INFO - 
📄 SCRAPING PAGE 3/10
2025-07-10 16:21:06,702 - INFO - ==================================================
2025-07-10 16:21:06,702 - INFO - 🔍 Extracting data from page 3...
2025-07-10 16:21:09,334 - INFO - 📸 Saved page 3 screenshot: scraper_output/screenshots/cidb_page_3_1752157269.png
2025-07-10 16:21:09,335 - INFO - 🤖 LLM extracting from CIDB page 3 content content (26295 chars)
2025-07-10 16:21:09,335 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:09,335 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_3_1752157269.png
2025-07-10 16:21:09,349 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:09,350 - INFO - 🎯 Page 3: Found 0 unique contractors
2025-07-10 16:21:09,350 - INFO - 📊 Page 3 Summary:
2025-07-10 16:21:09,350 - INFO -    Contractors found: 0
2025-07-10 16:21:09,350 - INFO -    Total so far: 0
2025-07-10 16:21:09,350 - INFO -    Pages completed: 3/10
2025-07-10 16:21:09,350 - INFO - 🔄 Navigating to page 4...
2025-07-10 16:21:09,350 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:11,497 - INFO - ✅ Successfully navigated to page 4 using Page Number Link
2025-07-10 16:21:14,499 - INFO - 
📄 SCRAPING PAGE 4/10
2025-07-10 16:21:14,499 - INFO - ==================================================
2025-07-10 16:21:14,499 - INFO - 🔍 Extracting data from page 4...
2025-07-10 16:21:17,064 - INFO - 📸 Saved page 4 screenshot: scraper_output/screenshots/cidb_page_4_1752157277.png
2025-07-10 16:21:17,064 - INFO - 🤖 LLM extracting from CIDB page 4 content content (26295 chars)
2025-07-10 16:21:17,064 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:17,064 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_4_1752157277.png
2025-07-10 16:21:17,078 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:17,078 - INFO - 🎯 Page 4: Found 0 unique contractors
2025-07-10 16:21:17,079 - INFO - 📊 Page 4 Summary:
2025-07-10 16:21:17,079 - INFO -    Contractors found: 0
2025-07-10 16:21:17,079 - INFO -    Total so far: 0
2025-07-10 16:21:17,079 - INFO -    Pages completed: 4/10
2025-07-10 16:21:17,079 - INFO - 🔄 Navigating to page 5...
2025-07-10 16:21:17,079 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:19,865 - INFO - ✅ Successfully navigated to page 5 using Page Number Link
2025-07-10 16:21:22,867 - INFO - 
📄 SCRAPING PAGE 5/10
2025-07-10 16:21:22,868 - INFO - ==================================================
2025-07-10 16:21:22,868 - INFO - 🔍 Extracting data from page 5...
2025-07-10 16:21:25,903 - INFO - 📸 Saved page 5 screenshot: scraper_output/screenshots/cidb_page_5_1752157285.png
2025-07-10 16:21:25,904 - INFO - 🤖 LLM extracting from CIDB page 5 content content (26295 chars)
2025-07-10 16:21:25,904 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:25,904 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_5_1752157285.png
2025-07-10 16:21:25,919 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:25,919 - INFO - 🎯 Page 5: Found 0 unique contractors
2025-07-10 16:21:25,920 - INFO - 📊 Page 5 Summary:
2025-07-10 16:21:25,920 - INFO -    Contractors found: 0
2025-07-10 16:21:25,920 - INFO -    Total so far: 0
2025-07-10 16:21:25,920 - INFO -    Pages completed: 5/10
2025-07-10 16:21:25,920 - INFO - 🔄 Navigating to page 6...
2025-07-10 16:21:25,920 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:28,816 - INFO - ✅ Successfully navigated to page 6 using Page Number Link
2025-07-10 16:21:31,818 - INFO - 
📄 SCRAPING PAGE 6/10
2025-07-10 16:21:31,818 - INFO - ==================================================
2025-07-10 16:21:31,818 - INFO - 🔍 Extracting data from page 6...
2025-07-10 16:21:34,835 - INFO - 📸 Saved page 6 screenshot: scraper_output/screenshots/cidb_page_6_1752157294.png
2025-07-10 16:21:34,835 - INFO - 🤖 LLM extracting from CIDB page 6 content content (26295 chars)
2025-07-10 16:21:34,835 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:34,835 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_6_1752157294.png
2025-07-10 16:21:34,850 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:34,851 - INFO - 🎯 Page 6: Found 0 unique contractors
2025-07-10 16:21:34,851 - INFO - 📊 Page 6 Summary:
2025-07-10 16:21:34,851 - INFO -    Contractors found: 0
2025-07-10 16:21:34,851 - INFO -    Total so far: 0
2025-07-10 16:21:34,851 - INFO -    Pages completed: 6/10
2025-07-10 16:21:34,851 - INFO - 🔄 Navigating to page 7...
2025-07-10 16:21:34,851 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:37,700 - INFO - ✅ Successfully navigated to page 7 using Page Number Link
2025-07-10 16:21:40,701 - INFO - 
📄 SCRAPING PAGE 7/10
2025-07-10 16:21:40,702 - INFO - ==================================================
2025-07-10 16:21:40,702 - INFO - 🔍 Extracting data from page 7...
2025-07-10 16:21:43,621 - INFO - 📸 Saved page 7 screenshot: scraper_output/screenshots/cidb_page_7_1752157303.png
2025-07-10 16:21:43,621 - INFO - 🤖 LLM extracting from CIDB page 7 content content (26295 chars)
2025-07-10 16:21:43,622 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:43,622 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_7_1752157303.png
2025-07-10 16:21:43,635 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:43,635 - INFO - 🎯 Page 7: Found 0 unique contractors
2025-07-10 16:21:43,636 - INFO - 📊 Page 7 Summary:
2025-07-10 16:21:43,636 - INFO -    Contractors found: 0
2025-07-10 16:21:43,636 - INFO -    Total so far: 0
2025-07-10 16:21:43,636 - INFO -    Pages completed: 7/10
2025-07-10 16:21:43,636 - INFO - 🔄 Navigating to page 8...
2025-07-10 16:21:43,636 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:46,565 - INFO - ✅ Successfully navigated to page 8 using Page Number Link
2025-07-10 16:21:49,567 - INFO - 
📄 SCRAPING PAGE 8/10
2025-07-10 16:21:49,567 - INFO - ==================================================
2025-07-10 16:21:49,567 - INFO - 🔍 Extracting data from page 8...
2025-07-10 16:21:52,488 - INFO - 📸 Saved page 8 screenshot: scraper_output/screenshots/cidb_page_8_1752157312.png
2025-07-10 16:21:52,488 - INFO - 🤖 LLM extracting from CIDB page 8 content content (26295 chars)
2025-07-10 16:21:52,488 - WARNING - No LLM clients available for extraction
2025-07-10 16:21:52,488 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_8_1752157312.png
2025-07-10 16:21:52,502 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:21:52,503 - INFO - 🎯 Page 8: Found 0 unique contractors
2025-07-10 16:21:52,503 - INFO - 📊 Page 8 Summary:
2025-07-10 16:21:52,503 - INFO -    Contractors found: 0
2025-07-10 16:21:52,503 - INFO -    Total so far: 0
2025-07-10 16:21:52,503 - INFO -    Pages completed: 8/10
2025-07-10 16:21:52,503 - INFO - 🔄 Navigating to page 9...
2025-07-10 16:21:52,503 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:21:55,318 - INFO - ✅ Successfully navigated to page 9 using Page Number Link
2025-07-10 16:21:58,320 - INFO - 
📄 SCRAPING PAGE 9/10
2025-07-10 16:21:58,320 - INFO - ==================================================
2025-07-10 16:21:58,321 - INFO - 🔍 Extracting data from page 9...
2025-07-10 16:22:01,268 - INFO - 📸 Saved page 9 screenshot: scraper_output/screenshots/cidb_page_9_1752157321.png
2025-07-10 16:22:01,268 - INFO - 🤖 LLM extracting from CIDB page 9 content content (26295 chars)
2025-07-10 16:22:01,268 - WARNING - No LLM clients available for extraction
2025-07-10 16:22:01,268 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_9_1752157321.png
2025-07-10 16:22:01,282 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:22:01,283 - INFO - 🎯 Page 9: Found 0 unique contractors
2025-07-10 16:22:01,283 - INFO - 📊 Page 9 Summary:
2025-07-10 16:22:01,283 - INFO -    Contractors found: 0
2025-07-10 16:22:01,283 - INFO -    Total so far: 0
2025-07-10 16:22:01,283 - INFO -    Pages completed: 9/10
2025-07-10 16:22:01,283 - INFO - 🔄 Navigating to page 10...
2025-07-10 16:22:01,283 - INFO -    Trying Page Number Link strategy...
2025-07-10 16:22:04,099 - INFO - ✅ Successfully navigated to page 10 using Page Number Link
2025-07-10 16:22:07,101 - INFO - 
📄 SCRAPING PAGE 10/10
2025-07-10 16:22:07,101 - INFO - ==================================================
2025-07-10 16:22:07,101 - INFO - 🔍 Extracting data from page 10...
2025-07-10 16:22:10,302 - INFO - 📸 Saved page 10 screenshot: scraper_output/screenshots/cidb_page_10_1752157330.png
2025-07-10 16:22:10,303 - INFO - 🤖 LLM extracting from CIDB page 10 content content (26295 chars)
2025-07-10 16:22:10,303 - WARNING - No LLM clients available for extraction
2025-07-10 16:22:10,304 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_10_1752157330.png
2025-07-10 16:22:10,319 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:22:10,320 - INFO - 🎯 Page 10: Found 0 unique contractors
2025-07-10 16:22:10,320 - INFO - 📊 Page 10 Summary:
2025-07-10 16:22:10,320 - INFO -    Contractors found: 0
2025-07-10 16:22:10,320 - INFO -    Total so far: 0
2025-07-10 16:22:10,320 - INFO -    Pages completed: 10/10
2025-07-10 16:22:14,020 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.cd7e030d-5ce3-4ae4-9030-5de03752bf66 "HTTP/2 200 OK"
2025-07-10 16:22:14,028 - INFO - 📊 Updated scraping run cd7e030d-5ce3-4ae4-9030-5de03752bf66 with status: completed
2025-07-10 16:22:14,030 - INFO - 
🎉 CIDB PAGINATION SCRAPING COMPLETED!
2025-07-10 16:22:14,030 - INFO - ============================================================
2025-07-10 16:22:14,030 - INFO - 📊 Final Results:
2025-07-10 16:22:14,030 - INFO -    Pages scraped: 10/10
2025-07-10 16:22:14,030 - INFO -    Total contractors: 0
2025-07-10 16:22:14,030 - INFO -    Duration: 83.70 seconds
2025-07-10 16:22:14,030 - INFO -    Average per page: 8.37 seconds
2025-07-10 16:22:14,030 - INFO -    Report saved: scraper_output/cidb_pagination_report_cd7e030d-5ce3-4ae4-9030-5de03752bf66.json
2025-07-10 16:25:13,018 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 16:25:13,021 - ERROR - Failed to initialize scraping run: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (15, ac12d0eb-c8d8-4fac-8086-5d9e8b4e641f, started, cidb_interactive, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:25:12.874325+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:25:13,021 - ERROR - Critical error during interactive scraping: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (15, ac12d0eb-c8d8-4fac-8086-5d9e8b4e641f, started, cidb_interactive, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:25:12.874325+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:25:13,232 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.ac12d0eb-c8d8-4fac-8086-5d9e8b4e641f "HTTP/2 200 OK"
2025-07-10 16:25:13,238 - INFO - 📊 Updated scraping run ac12d0eb-c8d8-4fac-8086-5d9e8b4e641f with status: failed
2025-07-10 16:25:13,238 - ERROR - Interactive scraping failed: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (15, ac12d0eb-c8d8-4fac-8086-5d9e8b4e641f, started, cidb_interactive, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:25:12.874325+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:34:30,529 - INFO - 🔥 FireCrawl scraping: https://httpbin.org/html
2025-07-10 16:34:31,112 - ERROR - FireCrawl scraping failed: Unexpected error during scrape URL: Status code 400. Bad Request - [{'code': 'unrecognized_keys', 'keys': ['params'], 'path': [], 'message': 'Unrecognized key in body -- please review the v1 API documentation for request body changes'}]
2025-07-10 16:34:31,147 - INFO - 🤖 LLM extracting from test contractor data content (929 chars)
2025-07-10 16:34:42,654 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 16:34:42,664 - INFO - ✅ OpenAI extracted 3 contractors
2025-07-10 16:34:42,700 - INFO - 🔄 Starting hybrid scrape of: https://httpbin.org/html
2025-07-10 16:34:44,706 - INFO - 🔥 FireCrawl scraping: https://httpbin.org/html
2025-07-10 16:34:45,014 - ERROR - FireCrawl scraping failed: Unexpected error during scrape URL: Status code 400. Bad Request - [{'code': 'unrecognized_keys', 'keys': ['params'], 'path': [], 'message': 'Unrecognized key in body -- please review the v1 API documentation for request body changes'}]
2025-07-10 16:34:45,015 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:34:49,920 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752158089.png
2025-07-10 16:34:50,003 - INFO - 🤖 LLM extracting from markdown content (3598 chars)
2025-07-10 16:34:51,014 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 16:34:51,017 - INFO - ✅ OpenAI extracted 0 contractors
2025-07-10 16:34:51,018 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_9_1752157321.png
2025-07-10 16:34:51,058 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:34:51,058 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/crawl4ai_1752156186.png
2025-07-10 16:34:51,127 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:34:51,127 - INFO - 🎯 Hybrid scrape completed. Methods used: 
2025-07-10 16:34:53,149 - INFO - 🔥 FireCrawl scraping: https://portal.cidb.org.za/RegisterOfContractors/
2025-07-10 16:34:53,474 - ERROR - FireCrawl scraping failed: Unexpected error during scrape URL: Status code 400. Bad Request - [{'code': 'unrecognized_keys', 'keys': ['params'], 'path': [], 'message': 'Unrecognized key in body -- please review the v1 API documentation for request body changes'}]
2025-07-10 16:34:53,475 - INFO - 🔄 Starting hybrid scrape of: https://portal.cidb.org.za/RegisterOfContractors/
2025-07-10 16:34:55,481 - INFO - 🔥 FireCrawl scraping: https://portal.cidb.org.za/RegisterOfContractors/
2025-07-10 16:34:55,793 - ERROR - FireCrawl scraping failed: Unexpected error during scrape URL: Status code 400. Bad Request - [{'code': 'unrecognized_keys', 'keys': ['params'], 'path': [], 'message': 'Unrecognized key in body -- please review the v1 API documentation for request body changes'}]
2025-07-10 16:34:55,794 - INFO - 🕷️ Crawl4AI scraping: https://portal.cidb.org.za/RegisterOfContractors/
2025-07-10 16:34:59,490 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752158099.png
2025-07-10 16:34:59,560 - INFO - 🤖 LLM extracting from markdown content (26295 chars)
2025-07-10 16:35:01,265 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-10 16:35:01,268 - INFO - ✅ OpenAI extracted 0 contractors
2025-07-10 16:35:01,269 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/cidb_page_9_1752157321.png
2025-07-10 16:35:01,294 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:35:01,294 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/crawl4ai_1752156186.png
2025-07-10 16:35:01,368 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:35:01,368 - INFO - 🎯 Hybrid scrape completed. Methods used: 
2025-07-10 16:36:31,196 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 16:36:31,199 - ERROR - Failed to initialize scraping run: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (16, d7705300-0704-450c-93fa-953f1a99fc10, started, final_cidb_attempt, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:36:31.20157+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:43:14,764 - INFO - 🔥 FireCrawl scraping: https://portal.cidb.org.za/RegisterOfContractors/
2025-07-10 16:43:14,765 - ERROR - FireCrawl scraping failed: scrape_url() takes 2 positional arguments but 3 were given
