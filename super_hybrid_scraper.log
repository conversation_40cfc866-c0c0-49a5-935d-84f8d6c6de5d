2025-07-10 16:06:38,846 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 201 Created"
2025-07-10 16:06:38,850 - INFO - 🚀 Initialized hybrid scraping run: 0c90e1ae-2055-4c60-b575-0c56878cfa37
2025-07-10 16:06:39,396 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.0c90e1ae-2055-4c60-b575-0c56878cfa37 "HTTP/2 200 OK"
2025-07-10 16:06:39,398 - INFO - 📊 Updated scraping run 0c90e1ae-2055-4c60-b575-0c56878cfa37 with status: completed
2025-07-10 16:06:39,398 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:06:46,645 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156406.png
2025-07-10 16:07:56,290 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 16:07:56,292 - ERROR - Failed to initialize scraping run: {'message': 'new row for relation "scraping_runs" violates check constraint "scraping_runs_contractor_type_check"', 'code': '23514', 'hint': None, 'details': 'Failing row contains (12, 31bf2c4d-9433-4c13-aebe-f0c8d85d4c84, started, demo, 0, 0, 0, 0, 0, 0, null, 2025-07-10 14:07:56.172548+00, null, null, 5.0.0-hybrid, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36, Super hybrid scraper with FireCrawl, Crawl4AI, LLM, and screensh...).'}
2025-07-10 16:07:56,292 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:08:00,983 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156480.png
2025-07-10 16:08:01,056 - INFO - 🕷️ Crawl4AI scraping: https://example.com
2025-07-10 16:08:02,961 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156482.png
2025-07-10 16:08:03,049 - INFO - 🔍 Processing file: demo_contractors.txt (type: text/plain, ext: .txt)
2025-07-10 16:08:03,049 - INFO - 🤖 LLM extracting from text file (.txt) content (619 chars)
2025-07-10 16:08:03,049 - WARNING - No LLM clients available for extraction
2025-07-10 16:08:09,053 - INFO - 🔄 Starting hybrid scrape of: https://httpbin.org/html
2025-07-10 16:08:09,053 - INFO - 🕷️ Crawl4AI scraping: https://httpbin.org/html
2025-07-10 16:08:38,359 - INFO - 📸 Saved Crawl4AI screenshot: scraper_output/screenshots/crawl4ai_1752156518.png
2025-07-10 16:08:38,436 - INFO - 🤖 LLM extracting from markdown content (3598 chars)
2025-07-10 16:08:38,436 - WARNING - No LLM clients available for extraction
2025-07-10 16:08:38,437 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/crawl4ai_1752156480.png
2025-07-10 16:08:38,929 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:08:38,929 - INFO - 📸 Analyzing screenshot: scraper_output/screenshots/crawl4ai_1752156186.png
2025-07-10 16:08:38,989 - ERROR - Screenshot analysis failed: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-10 16:08:38,989 - INFO - 🎯 Hybrid scrape completed. Methods used: 
