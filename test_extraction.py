#!/usr/bin/env python3
"""
Test extraction functionality with a limited scrape
"""

import asyncio
import json
from cidb_scraper import CIDBScraper

async def test_single_page_extraction():
    """Test extraction from a single page"""
    print("🧪 Testing single page extraction...")
    
    scraper = CIDBScraper()
    
    try:
        # Initialize scraping run
        await scraper.initialize_scraping_run('active')
        
        # Test scraping just one page of active contractors
        contractors = await scraper.scrape_contractors_by_status('active')
        
        if contractors:
            print(f"✅ Successfully extracted {len(contractors)} contractors")
            
            # Show sample data
            if len(contractors) > 0:
                print("\n📋 Sample contractor data:")
                sample = contractors[0]
                for key, value in sample.items():
                    print(f"  {key}: {value}")
            
            # Update scraping run as completed
            await scraper.update_scraping_run('completed')
            
            return True
        else:
            print("❌ No contractors extracted")
            await scraper.update_scraping_run('failed')
            return False
            
    except Exception as e:
        print(f"❌ Extraction test failed: {str(e)}")
        await scraper.update_scraping_run('failed')
        return False

if __name__ == "__main__":
    success = asyncio.run(test_single_page_extraction())
    if success:
        print("\n🎉 Extraction test successful! Ready for full scrape.")
    else:
        print("\n⚠️ Extraction test failed. Check the logs for details.")
    exit(0 if success else 1)
