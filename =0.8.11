Defaulting to user installation because normal site-packages is not writeable
Collecting python-docx
  Downloading python_docx-1.2.0-py3-none-any.whl.metadata (2.0 kB)
Requirement already satisfied: lxml>=3.1.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from python-docx) (5.4.0)
Requirement already satisfied: typing_extensions>=4.9.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from python-docx) (4.14.1)
Downloading python_docx-1.2.0-py3-none-any.whl (252 kB)
Installing collected packages: python-docx
Successfully installed python-docx-1.2.0
