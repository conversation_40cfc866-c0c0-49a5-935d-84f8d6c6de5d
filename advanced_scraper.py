#!/usr/bin/env python3
"""
Advanced CIDB Contractor <PERSON><PERSON><PERSON>
Attempts multiple strategies to access contractor data
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
CIDB_BASE_URL = "https://portal.cidb.org.za/RegisterOfContractors/"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingStats:
    """Track scraping statistics"""
    run_id: str
    total_pages: int = 0
    total_contractors: int = 0
    active_contractors: int = 0
    inactive_contractors: int = 0
    new_contractors: int = 0
    updated_contractors: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class AdvancedCIDBScraper:
    """Advanced CIDB Contractor Scraper with multiple access strategies"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
    
    async def initialize_scraping_run(self, contractor_type: str) -> str:
        """Initialize a new scraping run in the database"""
        try:
            result = supabase.table('scraping_runs').insert({
                'run_id': self.stats.run_id,
                'status': 'started',
                'contractor_type': contractor_type,
                'scraper_version': '3.0.0',
                'user_agent': self.browser_config.user_agent,
                'notes': 'Advanced scraper with multiple access strategies'
            }).execute()
            
            logger.info(f"Initialized scraping run: {self.stats.run_id}")
            return self.stats.run_id
            
        except Exception as e:
            logger.error(f"Failed to initialize scraping run: {str(e)}")
            raise
    
    async def update_scraping_run(self, status: str, end_time: Optional[datetime] = None):
        """Update scraping run status and statistics"""
        try:
            update_data = {
                'status': status,
                'total_pages_scraped': self.stats.total_pages,
                'total_contractors_found': self.stats.total_contractors,
                'active_contractors_count': self.stats.active_contractors,
                'inactive_contractors_count': self.stats.inactive_contractors,
                'new_contractors_added': self.stats.new_contractors,
                'updated_contractors_count': self.stats.updated_contractors,
                'errors_encountered': self.stats.errors
            }
            
            if end_time:
                update_data['end_time'] = end_time.isoformat()
            
            supabase.table('scraping_runs').update(update_data).eq('run_id', self.stats.run_id).execute()
            logger.info(f"Updated scraping run {self.stats.run_id} with status: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update scraping run: {str(e)}")
    
    async def try_search_bypass(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try to bypass authentication by using search functionality"""
        contractors = []
        
        try:
            logger.info("🔍 Attempting search bypass strategy...")
            
            # Strategy 1: Try to trigger a search that might return data
            search_js = """
            (async () => {
                try {
                    // Try to find and click the search button
                    const searchBtn = document.querySelector('input[type="submit"][value*="Search"], button[type="submit"], .btn-search, #search-btn');
                    if (searchBtn) {
                        searchBtn.click();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        return 'search_clicked';
                    }
                    
                    // Try to find and submit any form
                    const forms = document.querySelectorAll('form');
                    if (forms.length > 0) {
                        forms[0].submit();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        return 'form_submitted';
                    }
                    
                    return 'no_search_found';
                } catch (e) {
                    return 'error: ' + e.message;
                }
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[search_js],
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                wait_for="css:body"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            
            if result.success:
                logger.info(f"Search bypass result: {result.js_execution_result}")
                
                # Try to extract data after search
                contractors = await self.extract_any_contractor_data(crawler)
                
        except Exception as e:
            logger.error(f"Search bypass failed: {str(e)}")
            self.stats.errors.append(f"Search bypass error: {str(e)}")
        
        return contractors
    
    async def try_direct_api_access(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try to find and access any API endpoints"""
        contractors = []
        
        try:
            logger.info("🌐 Attempting direct API access...")
            
            # Look for API calls in network requests
            api_discovery_js = """
            (async () => {
                try {
                    // Intercept fetch requests
                    const originalFetch = window.fetch;
                    const requests = [];
                    
                    window.fetch = function(...args) {
                        requests.push(args[0]);
                        return originalFetch.apply(this, args);
                    };
                    
                    // Trigger any AJAX calls
                    const buttons = document.querySelectorAll('button, input[type="button"], a[href*="javascript"]');
                    for (let btn of buttons) {
                        try {
                            btn.click();
                            await new Promise(resolve => setTimeout(resolve, 500));
                        } catch (e) {}
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    return {
                        requests: requests,
                        xhr_count: window.XMLHttpRequest ? 'available' : 'not_available'
                    };
                } catch (e) {
                    return 'error: ' + e.message;
                }
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[api_discovery_js],
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                wait_for="css:body"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            
            if result.success and result.js_execution_result:
                logger.info(f"API discovery result: {result.js_execution_result}")
                
        except Exception as e:
            logger.error(f"API access attempt failed: {str(e)}")
            self.stats.errors.append(f"API access error: {str(e)}")
        
        return contractors
    
    async def try_iframe_content(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try to extract content from any iframes"""
        contractors = []
        
        try:
            logger.info("🖼️ Checking for iframe content...")
            
            iframe_js = """
            (async () => {
                try {
                    const iframes = document.querySelectorAll('iframe');
                    const iframeData = [];
                    
                    for (let iframe of iframes) {
                        try {
                            const src = iframe.src;
                            const content = iframe.contentDocument ? iframe.contentDocument.body.innerHTML : 'no_access';
                            iframeData.push({
                                src: src,
                                hasContent: content !== 'no_access',
                                contentLength: content.length
                            });
                        } catch (e) {
                            iframeData.push({
                                src: iframe.src,
                                error: e.message
                            });
                        }
                    }
                    
                    return {
                        iframeCount: iframes.length,
                        iframes: iframeData
                    };
                } catch (e) {
                    return 'error: ' + e.message;
                }
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[iframe_js],
                cache_mode=CacheMode.BYPASS,
                page_timeout=15000,
                wait_for="css:body"
            )
            
            result = await crawler.arun(url=CIDB_BASE_URL, config=config)
            
            if result.success and result.js_execution_result:
                logger.info(f"Iframe analysis: {result.js_execution_result}")
                
        except Exception as e:
            logger.error(f"Iframe content check failed: {str(e)}")
            self.stats.errors.append(f"Iframe error: {str(e)}")
        
        return contractors
    
    async def extract_any_contractor_data(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Try multiple extraction strategies"""
        contractors = []
        
        try:
            # Strategy 1: Look for any table with contractor-like data
            table_schemas = [
                {
                    "name": "Main Contractor Table",
                    "baseSelector": "#ctl00_ContentPlaceHolder1_gvContractors tr:not(:first-child)",
                    "fields": [
                        {"name": "crs_number", "selector": "td:nth-child(1)", "type": "text"},
                        {"name": "contractor_name", "selector": "td:nth-child(2)", "type": "text"},
                        {"name": "status", "selector": "td:nth-child(3)", "type": "text"},
                        {"name": "grading", "selector": "td:nth-child(4)", "type": "text"},
                        {"name": "expiry_date", "selector": "td:nth-child(5)", "type": "text"},
                        {"name": "trading_name", "selector": "td:nth-child(6)", "type": "text"},
                        {"name": "province", "selector": "td:nth-child(7)", "type": "text"},
                        {"name": "city", "selector": "td:nth-child(8)", "type": "text"},
                        {"name": "bbbee_status", "selector": "td:nth-child(9)", "type": "text"},
                        {"name": "potentially_emerging", "selector": "td:nth-child(10)", "type": "text"},
                        {"name": "contact_number", "selector": "td:nth-child(11)", "type": "text"},
                        {"name": "email_address", "selector": "td:nth-child(12)", "type": "text"}
                    ]
                },
                {
                    "name": "Any Table Data",
                    "baseSelector": "table tr:has(td)",
                    "fields": [
                        {"name": "col1", "selector": "td:nth-child(1)", "type": "text"},
                        {"name": "col2", "selector": "td:nth-child(2)", "type": "text"},
                        {"name": "col3", "selector": "td:nth-child(3)", "type": "text"},
                        {"name": "col4", "selector": "td:nth-child(4)", "type": "text"},
                        {"name": "col5", "selector": "td:nth-child(5)", "type": "text"}
                    ]
                }
            ]
            
            for schema in table_schemas:
                try:
                    extraction_strategy = JsonCssExtractionStrategy(schema, verbose=True)
                    config = CrawlerRunConfig(
                        extraction_strategy=extraction_strategy,
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=15000,
                        wait_for="css:body"
                    )
                    
                    result = await crawler.arun(url=CIDB_BASE_URL, config=config)
                    
                    if result.success and result.extracted_content:
                        extracted_data = json.loads(result.extracted_content)
                        if extracted_data:
                            logger.info(f"Found {len(extracted_data)} records with schema: {schema['name']}")
                            contractors.extend(extracted_data)
                            break
                            
                except Exception as e:
                    logger.warning(f"Schema {schema['name']} failed: {str(e)}")
                    continue
            
        except Exception as e:
            logger.error(f"Data extraction failed: {str(e)}")
            self.stats.errors.append(f"Extraction error: {str(e)}")
        
        return contractors
    
    async def save_contractors_to_supabase(self, contractors: List[Dict]):
        """Save contractors to Supabase with enhanced data mapping"""
        if not contractors:
            return
        
        try:
            processed_contractors = []
            
            for i, contractor in enumerate(contractors):
                # Map the data to our schema
                processed_contractor = {
                    'crs_number': contractor.get('crs_number') or contractor.get('col1') or f'EXTRACTED_{i+1}',
                    'contractor_name': contractor.get('contractor_name') or contractor.get('col2') or 'Unknown',
                    'status': contractor.get('status') or contractor.get('col3'),
                    'grading': contractor.get('grading') or contractor.get('col4'),
                    'expiry_date': contractor.get('expiry_date') or contractor.get('col5'),
                    'trading_name': contractor.get('trading_name'),
                    'province': contractor.get('province'),
                    'city': contractor.get('city'),
                    'bbbee_status': contractor.get('bbbee_status'),
                    'potentially_emerging': contractor.get('potentially_emerging'),
                    'contact_number': contractor.get('contact_number'),
                    'email_address': contractor.get('email_address'),
                    'contractor_status': 'extracted',
                    'scraped_at': datetime.utcnow().isoformat() + "Z"
                }
                
                # Only add if we have meaningful data
                if any(v and str(v).strip() for v in processed_contractor.values() if v):
                    processed_contractors.append(processed_contractor)
            
            if processed_contractors:
                # Batch insert
                batch_size = 100
                for i in range(0, len(processed_contractors), batch_size):
                    batch = processed_contractors[i:i+batch_size]
                    
                    result = supabase.table('contractors').upsert(
                        batch,
                        on_conflict='crs_number,contractor_status'
                    ).execute()
                    
                    if not hasattr(result, 'error') or not result.error:
                        logger.info(f"Saved {len(batch)} contractors to database")
                        self.stats.new_contractors += len(batch)
                        self.stats.total_contractors += len(batch)
                    else:
                        logger.error(f"Database save error: {result.error}")
                        
        except Exception as e:
            logger.error(f"Error saving contractors: {str(e)}")
            self.stats.errors.append(f"Save error: {str(e)}")
    
    async def run_advanced_scrape(self):
        """Run advanced scraping with multiple strategies"""
        start_time = datetime.utcnow()
        
        try:
            # Initialize scraping run
            await self.initialize_scraping_run('both')
            
            logger.info("🚀 Starting advanced CIDB contractor scraping...")
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                all_contractors = []
                
                # Strategy 1: Search bypass
                contractors = await self.try_search_bypass(crawler)
                all_contractors.extend(contractors)
                
                # Strategy 2: Direct API access
                contractors = await self.try_direct_api_access(crawler)
                all_contractors.extend(contractors)
                
                # Strategy 3: Iframe content
                contractors = await self.try_iframe_content(crawler)
                all_contractors.extend(contractors)
                
                # Strategy 4: Direct extraction
                contractors = await self.extract_any_contractor_data(crawler)
                all_contractors.extend(contractors)
                
                # Save any data we found
                if all_contractors:
                    await self.save_contractors_to_supabase(all_contractors)
                    logger.info(f"✅ Successfully extracted {len(all_contractors)} contractor records")
                else:
                    logger.warning("❌ No contractor data could be extracted with any strategy")
            
            # Update final statistics
            end_time = datetime.utcnow()
            
            if self.stats.errors:
                status = 'partial' if all_contractors else 'failed'
            else:
                status = 'completed'
            
            await self.update_scraping_run(status, end_time)
            
            # Log final results
            duration = (end_time - start_time).total_seconds()
            logger.info(f"""
🎯 Advanced scraping completed in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Status: {status}
Total Contractors: {self.stats.total_contractors}
Errors: {len(self.stats.errors)}
            """)
            
            return {
                'run_id': self.stats.run_id,
                'status': status,
                'total_contractors': self.stats.total_contractors,
                'duration_seconds': duration,
                'errors': self.stats.errors
            }
            
        except Exception as e:
            logger.error(f"Critical error during advanced scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise

async def main():
    """Main entry point for advanced scraper"""
    scraper = AdvancedCIDBScraper()
    
    try:
        result = await scraper.run_advanced_scrape()
        print(f"🎉 Advanced scraping completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Advanced scraping failed: {str(e)}")
        print(f"❌ Advanced scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
