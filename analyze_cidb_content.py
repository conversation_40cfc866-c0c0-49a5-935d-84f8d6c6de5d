#!/usr/bin/env python3
"""
Analyze CIDB page content to understand what we're actually getting
"""

import asyncio
import json
from pathlib import Path
from super_hybrid_scraper import <PERSON>HybridScraper
from crawl4ai import As<PERSON><PERSON>eb<PERSON>raw<PERSON>, BrowserConfig, <PERSON>rawlerRunConfig, CacheMode

async def analyze_cidb_page():
    """Analyze what the CIDB page actually contains"""
    print("🔍 ANALYZING CIDB PAGE CONTENT")
    print("=" * 50)
    
    scraper = SuperHybridScraper()
    url = "https://portal.cidb.org.za/RegisterOfContractors/"
    
    async with AsyncWebCrawler(config=scraper.browser_config) as crawler:
        
        # Get the page content
        config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            page_timeout=30000,
            wait_for="css:body",
            screenshot=True
        )
        
        result = await crawler.arun(url=url, config=config)
        
        if result.success:
            print(f"✅ Successfully loaded CIDB page")
            print(f"📄 Content length: {len(result.html)} characters")
            print(f"📝 Markdown length: {len(result.markdown)} characters")
            
            # Save the full content for analysis
            output_dir = Path("analysis_output")
            output_dir.mkdir(exist_ok=True)
            
            # Save HTML
            with open(output_dir / "cidb_page.html", "w", encoding="utf-8") as f:
                f.write(result.html)
            
            # Save Markdown
            with open(output_dir / "cidb_page.md", "w", encoding="utf-8") as f:
                f.write(result.markdown)
            
            # Save screenshot
            if result.screenshot:
                import base64
                with open(output_dir / "cidb_page.png", "wb") as f:
                    f.write(base64.b64decode(result.screenshot))
                print(f"📸 Screenshot saved: {output_dir}/cidb_page.png")
            
            # Analyze content
            html_lower = result.html.lower()
            markdown_lower = result.markdown.lower()
            
            print(f"\n🔍 CONTENT ANALYSIS:")
            print(f"=" * 30)
            
            # Check for authentication/permission issues
            auth_indicators = [
                'login', 'sign in', 'authentication', 'unauthorized', 
                'access denied', 'permission', 'forbidden', 'you don\'t have'
            ]
            
            found_auth_issues = []
            for indicator in auth_indicators:
                if indicator in markdown_lower:
                    found_auth_issues.append(indicator)
            
            if found_auth_issues:
                print(f"🚨 Authentication issues detected: {found_auth_issues}")
            else:
                print(f"✅ No obvious authentication barriers found")
            
            # Check for contractor-related content
            contractor_indicators = [
                'contractor', 'crs', 'registration', 'grading', 'cidb',
                'table', 'grid', 'data', 'records', 'results'
            ]
            
            found_contractor_content = []
            for indicator in contractor_indicators:
                if indicator in markdown_lower:
                    found_contractor_content.append(indicator)
            
            print(f"📋 Contractor-related content found: {found_contractor_content}")
            
            # Check for pagination elements
            pagination_indicators = [
                'page', 'next', 'previous', 'pagination', '1', '2', '3'
            ]
            
            found_pagination = []
            for indicator in pagination_indicators:
                if indicator in markdown_lower:
                    found_pagination.append(indicator)
            
            print(f"📄 Pagination elements found: {found_pagination}")
            
            # Check for JavaScript/dynamic content indicators
            js_indicators = [
                'javascript', 'loading', 'please wait', 'dynamic', 'ajax'
            ]
            
            found_js_content = []
            for indicator in js_indicators:
                if indicator in markdown_lower:
                    found_js_content.append(indicator)
            
            print(f"⚡ JavaScript/dynamic content indicators: {found_js_content}")
            
            # Show first 1000 characters of markdown content
            print(f"\n📝 FIRST 1000 CHARACTERS OF CONTENT:")
            print(f"-" * 50)
            print(result.markdown[:1000])
            print(f"-" * 50)
            
            # Look for specific table structures
            print(f"\n🔍 SEARCHING FOR TABLE STRUCTURES:")
            print(f"-" * 40)
            
            if '<table' in html_lower:
                print(f"✅ HTML tables found")
                # Count tables
                table_count = html_lower.count('<table')
                print(f"📊 Number of tables: {table_count}")
            else:
                print(f"❌ No HTML tables found")
            
            if '|' in result.markdown:
                print(f"✅ Markdown table format found")
                # Count potential table rows
                table_rows = result.markdown.count('|')
                print(f"📊 Potential table elements: {table_rows}")
            else:
                print(f"❌ No markdown table format found")
            
            # Check for specific CIDB elements
            print(f"\n🏗️ CIDB-SPECIFIC ELEMENTS:")
            print(f"-" * 30)
            
            cidb_elements = [
                'register of contractors', 'contractor search', 'crs number',
                'grading', 'status', 'active', 'inactive', 'province'
            ]
            
            for element in cidb_elements:
                if element in markdown_lower:
                    print(f"✅ Found: {element}")
                else:
                    print(f"❌ Missing: {element}")
            
            print(f"\n📁 Analysis files saved to: {output_dir}/")
            print(f"   - cidb_page.html (full HTML)")
            print(f"   - cidb_page.md (markdown content)")
            print(f"   - cidb_page.png (screenshot)")
            
            return {
                'success': True,
                'content_length': len(result.html),
                'markdown_length': len(result.markdown),
                'auth_issues': found_auth_issues,
                'contractor_content': found_contractor_content,
                'pagination_elements': found_pagination,
                'js_indicators': found_js_content,
                'has_tables': '<table' in html_lower,
                'output_dir': str(output_dir)
            }
            
        else:
            print(f"❌ Failed to load CIDB page: {result.error_message}")
            return {'success': False, 'error': result.error_message}

async def main():
    """Main analysis function"""
    try:
        result = await analyze_cidb_page()
        
        print(f"\n🎯 ANALYSIS SUMMARY:")
        print(f"=" * 30)
        
        if result['success']:
            print(f"✅ Page loaded successfully")
            print(f"📊 Content: {result['content_length']} chars")
            print(f"🚨 Auth issues: {len(result['auth_issues'])}")
            print(f"📋 Contractor content: {len(result['contractor_content'])}")
            print(f"📄 Pagination: {len(result['pagination_elements'])}")
            print(f"🔧 Tables found: {result['has_tables']}")
            
            if result['auth_issues']:
                print(f"\n⚠️ RECOMMENDATION: Authentication required")
                print(f"   The page likely requires login credentials")
            elif not result['contractor_content']:
                print(f"\n⚠️ RECOMMENDATION: No contractor data visible")
                print(f"   The page may be empty or require specific actions")
            elif not result['has_tables']:
                print(f"\n⚠️ RECOMMENDATION: Data may be dynamically loaded")
                print(f"   Try waiting longer or triggering JavaScript events")
            else:
                print(f"\n✅ RECOMMENDATION: Data should be extractable")
                print(f"   Try different extraction strategies")
        else:
            print(f"❌ Analysis failed: {result['error']}")
        
        return result
        
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
