#!/usr/bin/env python3
"""
Test script for CIDB Scraper
Tests basic functionality before running full scrape
"""

import asyncio
import json
from cidb_scraper import CIDBScraper

async def test_scraper_initialization():
    """Test scraper initialization"""
    print("🧪 Testing scraper initialization...")
    
    try:
        scraper = CIDBScraper()
        print(f"✅ Scraper initialized successfully")
        print(f"📊 Run ID: {scraper.stats.run_id}")
        print(f"🔧 Browser config: {scraper.browser_config.headless}")
        return True
    except Exception as e:
        print(f"❌ Scraper initialization failed: {str(e)}")
        return False

async def test_supabase_connection():
    """Test Supabase connection"""
    print("\n🧪 Testing Supabase connection...")
    
    try:
        from cidb_scraper import supabase
        
        # Test connection by querying scraping_runs table
        result = supabase.table('scraping_runs').select('*').limit(1).execute()
        print("✅ Supabase connection successful")
        return True
    except Exception as e:
        print(f"❌ Supabase connection failed: {str(e)}")
        return False

async def test_database_insert():
    """Test database insert functionality"""
    print("\n🧪 Testing database insert...")
    
    try:
        scraper = CIDBScraper()
        
        # Initialize a test scraping run
        run_id = await scraper.initialize_scraping_run('active')
        print(f"✅ Test scraping run created: {run_id}")
        
        # Update the run as completed
        await scraper.update_scraping_run('completed')
        print("✅ Scraping run updated successfully")
        
        return True
    except Exception as e:
        print(f"❌ Database insert test failed: {str(e)}")
        return False

async def test_basic_crawl():
    """Test basic crawling functionality"""
    print("\n🧪 Testing basic crawl functionality...")
    
    try:
        from crawl4ai import AsyncWebCrawler, BrowserConfig
        
        browser_config = BrowserConfig(headless=True, verbose=False)
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            # Test with a simple page first
            result = await crawler.arun(url="https://httpbin.org/html")
            
            if result.success:
                print("✅ Basic crawl test successful")
                print(f"📄 Content length: {len(result.markdown) if result.markdown else 0} characters")
                return True
            else:
                print(f"❌ Basic crawl failed: {result.error_message}")
                return False
                
    except Exception as e:
        print(f"❌ Basic crawl test failed: {str(e)}")
        return False

async def test_cidb_site_access():
    """Test access to CIDB site"""
    print("\n🧪 Testing CIDB site access...")
    
    try:
        from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
        
        browser_config = BrowserConfig(headless=True, verbose=False)
        config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
        
        async with AsyncWebCrawler(config=browser_config) as crawler:
            result = await crawler.arun(
                url="https://portal.cidb.org.za/RegisterOfContractors/",
                config=config
            )
            
            if result.success:
                print("✅ CIDB site access successful")
                print(f"📄 Page title found: {'CIDB' in result.markdown if result.markdown else False}")
                
                # Check if we can find the contractor table
                if result.markdown and 'contractor' in result.markdown.lower():
                    print("✅ Contractor content detected on page")
                else:
                    print("⚠️ Contractor content not clearly detected")
                
                return True
            else:
                print(f"❌ CIDB site access failed: {result.error_message}")
                return False
                
    except Exception as e:
        print(f"❌ CIDB site access test failed: {str(e)}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Starting CIDB Scraper Tests...\n")
    
    tests = [
        ("Scraper Initialization", test_scraper_initialization),
        ("Supabase Connection", test_supabase_connection),
        ("Database Insert", test_database_insert),
        ("Basic Crawl", test_basic_crawl),
        ("CIDB Site Access", test_cidb_site_access),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Scraper is ready for deployment.")
        return True
    else:
        print("⚠️ Some tests failed. Please fix issues before running full scrape.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
